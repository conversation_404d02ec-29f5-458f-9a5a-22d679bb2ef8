import pygame

pygame.init()  # INITIALIZE PYGAME HERE

# SETUP THE GAME WINDOW HERE
SCREEN_WIDTH = 800
SCREEN_HEIGHT = 600
screen = pygame.display.set_mode([SCREEN_WIDTH, SCREEN_HEIGHT])
pygame.display.set_caption("Sweet Caption")
clock = pygame.time.Clock()

# CREATE FUNCTIONS, VARIABLES AND OBJECTS HERE

# states (idle = 0, walk = 1, etc)
IDLE = 0
WALK = 1


# PLAYER SPRITE
#load in spritesheet
player_sheet = pygame.image.load("blob.png").convert_alpha()
#create player sprite
player = pygame.sprite.Sprite()
#set player's looks to a portion of the spritesheet (a frame)... (0,0) top left, 369 wide, 512 height)
player.image = player_sheet.subsurface(0, 0, 369, 512)
#scale the player (to 100 by 150 pixels in this example)
player.image = pygame.transform.scale(player.image,(100,150))
#make the player's position/collision box the same size as the image/frame we just did
player.rect = player.image.get_rect()
#set players x and y position in the screen
player.rect.x = 100
player.rect.y = 300

#number of frames in the player's idle animation
player.idle_frames = 4
#number of frames in the player's walking animation
player.walk_frames = 4
#set the players current state to IDLE
player.current_state = IDLE
#the player starts facing right, so set it to True - will set to false when he looks left
player.facing_right = True
#keeps track of the current frame we are animating
player.current_frame = 0
#this timer lets us switch to the next frame
player.animation_timer = 0
#animation speed - lower numbers is faster switching between frames.  higher is slower
player.animation_speed = 10

# WORM SPRITE
#load in spritesheet
worm_sheet = pygame.image.load("worm.png").convert_alpha()
#create worm sprite
worm = pygame.sprite.Sprite()
#set worm's looks to a portion of the spritesheet (a frame)... (0,0) top left, 369 wide, 512 height)
worm.image = worm_sheet.subsurface(0, 0, 369, 512)
#scale the worm (to 100 by 150 pixels in this example)
worm.image = pygame.transform.scale(worm.image,(100,150))
#make the worm's position/collision box the same size as the image/frame we just did
worm.rect = worm.image.get_rect()
#set worms x and y position in the screen
worm.rect.x = 400
worm.rect.y = 300

#number of frames in the worm's idle animation
worm.idle_frames = 4
#number of frames in the worm's walking animation
worm.walk_frames = 4
#set the worms current state to IDLE
worm.current_state = IDLE
#the worm starts facing right, so set it to True - will set to false when he looks left
worm.facing_right = True
#keeps track of the current frame we are animating
worm.current_frame = 0
#this timer lets us switch to the next frame
worm.animation_timer = 0
#animation speed - lower numbers is faster switching between frames.  higher is slower
worm.animation_speed = 10


running = True
while running:

    pressed_keys = pygame.key.get_pressed()

    #if we press W A S or D, the player is WALKING, otherwise he is IDLE
    if pressed_keys[pygame.K_s] or pressed_keys[pygame.K_w] or pressed_keys[pygame.K_a] or pressed_keys[pygame.K_d]:
        player.current_state = WALK
    else:
        player.current_state = IDLE

    #if we press arrow keys, the worm is WALKING, otherwise he is IDLE
    if pressed_keys[pygame.K_DOWN] or pressed_keys[pygame.K_UP] or pressed_keys[pygame.K_LEFT] or pressed_keys[pygame.K_RIGHT]:
        worm.current_state = WALK
    else:
        worm.current_state = IDLE

    #increase the animation timer
    player.animation_timer += 1

    #if the animation timer is bigger than our speed, we can go to the next frame
    if player.animation_timer >= player.animation_speed:
        #reset the timer to 0
        player.animation_timer = 0
        #go to next frame
        player.current_frame += 1

        #if we are walking we will choose the next walk frame
        if player.current_state == WALK:
            #if our current frame is higher than number of total frames, reset to first frame
            if player.current_frame >= player.walk_frames:
                player.current_frame = 0
            #set player's image to the appropriate frame
            player.image = player_sheet.subsurface(player.current_frame*369, 512, 369, 512)

        #if we are idle we will choose the next idle frame
        elif player.current_state == IDLE:
            #if our current frame is higher than number of total frames, reset to first frame
            if player.current_frame >= player.idle_frames:
                player.current_frame = 0
            #set player's image to the appropriate frame
            player.image = player_sheet.subsurface(player.current_frame*369, 0, 369, 512)

        #scale the player again like we did near the start of the code
        player.image = pygame.transform.scale(player.image,(100,150))

        #if the player isnt facing right (so facing left), we flip him in the x direction
        if not player.facing_right:
            player.image = pygame.transform.flip(player.image,True,False)

    #increase the animation timer
    worm.animation_timer += 1

    #if the animation timer is bigger than our speed, we can go to the next frame
    if worm.animation_timer >= worm.animation_speed:
        #reset the timer to 0
        worm.animation_timer = 0
        #go to next frame
        worm.current_frame += 1

        #if we are walking we will choose the next walk frame
        if worm.current_state == WALK:
            #if our current frame is higher than number of total frames, reset to first frame
            if worm.current_frame >= worm.walk_frames:
                worm.current_frame = 0
            #set worm's image to the appropriate frame
            worm.image = worm_sheet.subsurface(worm.current_frame*369, 512, 369, 512)

        #if we are idle we will choose the next idle frame
        elif worm.current_state == IDLE:
            #if our current frame is higher than number of total frames, reset to first frame
            if worm.current_frame >= worm.idle_frames:
                worm.current_frame = 0
            #set worm's image to the appropriate frame
            worm.image = worm_sheet.subsurface(worm.current_frame*369, 0, 369, 512)

        #scale the worm again like we did near the start of the code
        worm.image = pygame.transform.scale(worm.image,(100,150))

        #if the worm isnt facing right (so facing left), we flip him in the x direction
        if not worm.facing_right:
            worm.image = pygame.transform.flip(worm.image,True,False)

    # EVENTS
    for event in pygame.event.get():
        if event.type == pygame.QUIT:
            running = False
            break

    if pressed_keys[pygame.K_ESCAPE]:
        running = False
        break

    if pressed_keys[pygame.K_s] and player.rect.y < SCREEN_HEIGHT - 20:  #DOWN PRESSED
        player.rect.move_ip(0,4)

    if pressed_keys[pygame.K_w] and player.rect.y > 0:  #UP PRESSED
        player.rect.move_ip(0,-4)

    if pressed_keys[pygame.K_a] and player.rect.x > 0:  #LEFT PRESSED
        player.rect.move_ip(-4,0)
        player.facing_right = False

    if pressed_keys[pygame.K_d] and player.rect.x < SCREEN_WIDTH - 20:  #RIGHT PRESSED
        player.rect.move_ip(4,0)
        player.facing_right = True

    if pressed_keys[pygame.K_DOWN] and worm.rect.y < SCREEN_HEIGHT - 20:  #DOWN PRESSED
        worm.rect.move_ip(0,4)

    if pressed_keys[pygame.K_UP] and worm.rect.y > 0:  #UP PRESSED
        worm.rect.move_ip(0,-4)

    if pressed_keys[pygame.K_LEFT] and worm.rect.x > 0:  #LEFT PRESSED
        worm.rect.move_ip(-4,0)
        worm.facing_right = False

    if pressed_keys[pygame.K_RIGHT] and worm.rect.x < SCREEN_WIDTH - 20:  #RIGHT PRESSED
        worm.rect.move_ip(4,0)
        worm.facing_right = True

    #UPDATES




    # DRAWS
    screen.fill((255,255,255))

    screen.blit(player.image, player.rect)
    screen.blit(worm.image, worm.rect)

    pygame.display.flip()
    clock.tick(60)
# END OF GAME LOOP

# We're outside the game loop, which means we want to end pygame!
pygame.quit()







