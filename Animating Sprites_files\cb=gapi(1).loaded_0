gapi.loaded_0(function(_){var window=this;
_._F_toggles_initialize=function(a){(typeof globalThis!=="undefined"?globalThis:typeof self!=="undefined"?self:this)._F_toggles=a||[]};(0,_._F_toggles_initialize)([]);
var da,ia,la,pa,ta,va,Da,Ea;da=function(a){var b=0;return function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}}};ia=typeof Object.defineProperties=="function"?Object.defineProperty:function(a,b,c){if(a==Array.prototype||a==Object.prototype)return a;a[b]=c.value;return a};
la=function(a){a=["object"==typeof globalThis&&globalThis,a,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global];for(var b=0;b<a.length;++b){var c=a[b];if(c&&c.Math==Math)return c}throw Error("a");};_.na=la(this);pa=function(a,b){if(b)a:{var c=_.na;a=a.split(".");for(var d=0;d<a.length-1;d++){var e=a[d];if(!(e in c))break a;c=c[e]}a=a[a.length-1];d=c[a];b=b(d);b!=d&&b!=null&&ia(c,a,{configurable:!0,writable:!0,value:b})}};
pa("Symbol",function(a){if(a)return a;var b=function(f,h){this.W1=f;ia(this,"description",{configurable:!0,writable:!0,value:h})};b.prototype.toString=function(){return this.W1};var c="jscomp_symbol_"+(Math.random()*1E9>>>0)+"_",d=0,e=function(f){if(this instanceof e)throw new TypeError("Symbol is not a constructor");return new b(c+(f||"")+"_"+d++,f)};return e});
pa("Symbol.iterator",function(a){if(a)return a;a=Symbol("Symbol.iterator");for(var b="Array Int8Array Uint8Array Uint8ClampedArray Int16Array Uint16Array Int32Array Uint32Array Float32Array Float64Array".split(" "),c=0;c<b.length;c++){var d=_.na[b[c]];typeof d==="function"&&typeof d.prototype[a]!="function"&&ia(d.prototype,a,{configurable:!0,writable:!0,value:function(){return ta(da(this))}})}return a});ta=function(a){a={next:a};a[Symbol.iterator]=function(){return this};return a};
_.ua=typeof Object.create=="function"?Object.create:function(a){var b=function(){};b.prototype=a;return new b};if(typeof Object.setPrototypeOf=="function")va=Object.setPrototypeOf;else{var wa;a:{var xa={a:!0},ya={};try{ya.__proto__=xa;wa=ya.a;break a}catch(a){}wa=!1}va=wa?function(a,b){a.__proto__=b;if(a.__proto__!==b)throw new TypeError(a+" is not extensible");return a}:null}_.za=va;
_.Ca=function(a){var b=typeof Symbol!="undefined"&&Symbol.iterator&&a[Symbol.iterator];if(b)return b.call(a);if(typeof a.length=="number")return{next:da(a)};throw Error("b`"+String(a));};Da=function(a,b){return Object.prototype.hasOwnProperty.call(a,b)};Ea=typeof Object.assign=="function"?Object.assign:function(a,b){for(var c=1;c<arguments.length;c++){var d=arguments[c];if(d)for(var e in d)Da(d,e)&&(a[e]=d[e])}return a};pa("Object.assign",function(a){return a||Ea});
pa("globalThis",function(a){return a||_.na});pa("Reflect.setPrototypeOf",function(a){return a?a:_.za?function(b,c){try{return(0,_.za)(b,c),!0}catch(d){return!1}}:null});
pa("Promise",function(a){function b(){this.Af=null}function c(h){return h instanceof e?h:new e(function(k){k(h)})}if(a)return a;b.prototype.wP=function(h){if(this.Af==null){this.Af=[];var k=this;this.xP(function(){k.D8()})}this.Af.push(h)};var d=_.na.setTimeout;b.prototype.xP=function(h){d(h,0)};b.prototype.D8=function(){for(;this.Af&&this.Af.length;){var h=this.Af;this.Af=[];for(var k=0;k<h.length;++k){var l=h[k];h[k]=null;try{l()}catch(m){this.Yp(m)}}}this.Af=null};b.prototype.Yp=function(h){this.xP(function(){throw h;
})};var e=function(h){this.Ca=0;this.tf=void 0;this.Er=[];this.VV=!1;var k=this.xF();try{h(k.resolve,k.reject)}catch(l){k.reject(l)}};e.prototype.xF=function(){function h(m){return function(n){l||(l=!0,m.call(k,n))}}var k=this,l=!1;return{resolve:h(this.Nea),reject:h(this.lK)}};e.prototype.Nea=function(h){if(h===this)this.lK(new TypeError("A Promise cannot resolve to itself"));else if(h instanceof e)this.sga(h);else{a:switch(typeof h){case "object":var k=h!=null;break a;case "function":k=!0;break a;
default:k=!1}k?this.Mea(h):this.TS(h)}};e.prototype.Mea=function(h){var k=void 0;try{k=h.then}catch(l){this.lK(l);return}typeof k=="function"?this.tga(k,h):this.TS(h)};e.prototype.lK=function(h){this.K_(2,h)};e.prototype.TS=function(h){this.K_(1,h)};e.prototype.K_=function(h,k){if(this.Ca!=0)throw Error("c`"+h+"`"+k+"`"+this.Ca);this.Ca=h;this.tf=k;this.Ca===2&&this.dfa();this.E8()};e.prototype.dfa=function(){var h=this;d(function(){if(h.Zca()){var k=_.na.console;typeof k!=="undefined"&&k.error(h.tf)}},
1)};e.prototype.Zca=function(){if(this.VV)return!1;var h=_.na.CustomEvent,k=_.na.Event,l=_.na.dispatchEvent;if(typeof l==="undefined")return!0;typeof h==="function"?h=new h("unhandledrejection",{cancelable:!0}):typeof k==="function"?h=new k("unhandledrejection",{cancelable:!0}):(h=_.na.document.createEvent("CustomEvent"),h.initCustomEvent("unhandledrejection",!1,!0,h));h.promise=this;h.reason=this.tf;return l(h)};e.prototype.E8=function(){if(this.Er!=null){for(var h=0;h<this.Er.length;++h)f.wP(this.Er[h]);
this.Er=null}};var f=new b;e.prototype.sga=function(h){var k=this.xF();h.qy(k.resolve,k.reject)};e.prototype.tga=function(h,k){var l=this.xF();try{h.call(k,l.resolve,l.reject)}catch(m){l.reject(m)}};e.prototype.then=function(h,k){function l(q,r){return typeof q=="function"?function(w){try{m(q(w))}catch(u){n(u)}}:r}var m,n,p=new e(function(q,r){m=q;n=r});this.qy(l(h,m),l(k,n));return p};e.prototype.catch=function(h){return this.then(void 0,h)};e.prototype.qy=function(h,k){function l(){switch(m.Ca){case 1:h(m.tf);
break;case 2:k(m.tf);break;default:throw Error("d`"+m.Ca);}}var m=this;this.Er==null?f.wP(l):this.Er.push(l);this.VV=!0};e.resolve=c;e.reject=function(h){return new e(function(k,l){l(h)})};e.race=function(h){return new e(function(k,l){for(var m=_.Ca(h),n=m.next();!n.done;n=m.next())c(n.value).qy(k,l)})};e.all=function(h){var k=_.Ca(h),l=k.next();return l.done?c([]):new e(function(m,n){function p(w){return function(u){q[w]=u;r--;r==0&&m(q)}}var q=[],r=0;do q.push(void 0),r++,c(l.value).qy(p(q.length-
1),n),l=k.next();while(!l.done)})};return e});var Ia=function(a,b,c){if(a==null)throw new TypeError("The 'this' value for String.prototype."+c+" must not be null or undefined");if(b instanceof RegExp)throw new TypeError("First argument to String.prototype."+c+" must not be a regular expression");return a+""};
pa("String.prototype.startsWith",function(a){return a?a:function(b,c){var d=Ia(this,b,"startsWith"),e=d.length,f=b.length;c=Math.max(0,Math.min(c|0,d.length));for(var h=0;h<f&&c<e;)if(d[c++]!=b[h++])return!1;return h>=f}});pa("Object.setPrototypeOf",function(a){return a||_.za});pa("Symbol.dispose",function(a){return a?a:Symbol("Symbol.dispose")});
pa("WeakMap",function(a){function b(){}function c(l){var m=typeof l;return m==="object"&&l!==null||m==="function"}function d(l){if(!Da(l,f)){var m=new b;ia(l,f,{value:m})}}function e(l){var m=Object[l];m&&(Object[l]=function(n){if(n instanceof b)return n;Object.isExtensible(n)&&d(n);return m(n)})}if(function(){if(!a||!Object.seal)return!1;try{var l=Object.seal({}),m=Object.seal({}),n=new a([[l,2],[m,3]]);if(n.get(l)!=2||n.get(m)!=3)return!1;n.delete(l);n.set(m,4);return!n.has(l)&&n.get(m)==4}catch(p){return!1}}())return a;
var f="$jscomp_hidden_"+Math.random();e("freeze");e("preventExtensions");e("seal");var h=0,k=function(l){this.Da=(h+=Math.random()+1).toString();if(l){l=_.Ca(l);for(var m;!(m=l.next()).done;)m=m.value,this.set(m[0],m[1])}};k.prototype.set=function(l,m){if(!c(l))throw Error("e");d(l);if(!Da(l,f))throw Error("f`"+l);l[f][this.Da]=m;return this};k.prototype.get=function(l){return c(l)&&Da(l,f)?l[f][this.Da]:void 0};k.prototype.has=function(l){return c(l)&&Da(l,f)&&Da(l[f],this.Da)};k.prototype.delete=
function(l){return c(l)&&Da(l,f)&&Da(l[f],this.Da)?delete l[f][this.Da]:!1};return k});
pa("Map",function(a){if(function(){if(!a||typeof a!="function"||!a.prototype.entries||typeof Object.seal!="function")return!1;try{var k=Object.seal({x:4}),l=new a(_.Ca([[k,"s"]]));if(l.get(k)!="s"||l.size!=1||l.get({x:4})||l.set({x:4},"t")!=l||l.size!=2)return!1;var m=l.entries(),n=m.next();if(n.done||n.value[0]!=k||n.value[1]!="s")return!1;n=m.next();return n.done||n.value[0].x!=4||n.value[1]!="t"||!m.next().done?!1:!0}catch(p){return!1}}())return a;var b=new WeakMap,c=function(k){this[0]={};this[1]=
f();this.size=0;if(k){k=_.Ca(k);for(var l;!(l=k.next()).done;)l=l.value,this.set(l[0],l[1])}};c.prototype.set=function(k,l){k=k===0?0:k;var m=d(this,k);m.list||(m.list=this[0][m.id]=[]);m.We?m.We.value=l:(m.We={next:this[1],Jk:this[1].Jk,head:this[1],key:k,value:l},m.list.push(m.We),this[1].Jk.next=m.We,this[1].Jk=m.We,this.size++);return this};c.prototype.delete=function(k){k=d(this,k);return k.We&&k.list?(k.list.splice(k.index,1),k.list.length||delete this[0][k.id],k.We.Jk.next=k.We.next,k.We.next.Jk=
k.We.Jk,k.We.head=null,this.size--,!0):!1};c.prototype.clear=function(){this[0]={};this[1]=this[1].Jk=f();this.size=0};c.prototype.has=function(k){return!!d(this,k).We};c.prototype.get=function(k){return(k=d(this,k).We)&&k.value};c.prototype.entries=function(){return e(this,function(k){return[k.key,k.value]})};c.prototype.keys=function(){return e(this,function(k){return k.key})};c.prototype.values=function(){return e(this,function(k){return k.value})};c.prototype.forEach=function(k,l){for(var m=this.entries(),
n;!(n=m.next()).done;)n=n.value,k.call(l,n[1],n[0],this)};c.prototype[Symbol.iterator]=c.prototype.entries;var d=function(k,l){var m=l&&typeof l;m=="object"||m=="function"?b.has(l)?m=b.get(l):(m=""+ ++h,b.set(l,m)):m="p_"+l;var n=k[0][m];if(n&&Da(k[0],m))for(k=0;k<n.length;k++){var p=n[k];if(l!==l&&p.key!==p.key||l===p.key)return{id:m,list:n,index:k,We:p}}return{id:m,list:n,index:-1,We:void 0}},e=function(k,l){var m=k[1];return ta(function(){if(m){for(;m.head!=k[1];)m=m.Jk;for(;m.next!=m.head;)return m=
m.next,{done:!1,value:l(m)};m=null}return{done:!0,value:void 0}})},f=function(){var k={};return k.Jk=k.next=k.head=k},h=0;return c});
pa("Set",function(a){if(function(){if(!a||typeof a!="function"||!a.prototype.entries||typeof Object.seal!="function")return!1;try{var c=Object.seal({x:4}),d=new a(_.Ca([c]));if(!d.has(c)||d.size!=1||d.add(c)!=d||d.size!=1||d.add({x:4})!=d||d.size!=2)return!1;var e=d.entries(),f=e.next();if(f.done||f.value[0]!=c||f.value[1]!=c)return!1;f=e.next();return f.done||f.value[0]==c||f.value[0].x!=4||f.value[1]!=f.value[0]?!1:e.next().done}catch(h){return!1}}())return a;var b=function(c){this.Sa=new Map;if(c){c=
_.Ca(c);for(var d;!(d=c.next()).done;)this.add(d.value)}this.size=this.Sa.size};b.prototype.add=function(c){c=c===0?0:c;this.Sa.set(c,c);this.size=this.Sa.size;return this};b.prototype.delete=function(c){c=this.Sa.delete(c);this.size=this.Sa.size;return c};b.prototype.clear=function(){this.Sa.clear();this.size=0};b.prototype.has=function(c){return this.Sa.has(c)};b.prototype.entries=function(){return this.Sa.entries()};b.prototype.values=function(){return this.Sa.values()};b.prototype.keys=b.prototype.values;
b.prototype[Symbol.iterator]=b.prototype.values;b.prototype.forEach=function(c,d){var e=this;this.Sa.forEach(function(f){return c.call(d,f,f,e)})};return b});var Ka=function(a,b){a instanceof String&&(a+="");var c=0,d=!1,e={next:function(){if(!d&&c<a.length){var f=c++;return{value:b(f,a[f]),done:!1}}d=!0;return{done:!0,value:void 0}}};e[Symbol.iterator]=function(){return e};return e};pa("Array.prototype.entries",function(a){return a?a:function(){return Ka(this,function(b,c){return[b,c]})}});
pa("Array.prototype.keys",function(a){return a?a:function(){return Ka(this,function(b){return b})}});pa("String.prototype.codePointAt",function(a){return a?a:function(b){var c=Ia(this,null,"codePointAt"),d=c.length;b=Number(b)||0;if(b>=0&&b<d){b|=0;var e=c.charCodeAt(b);if(e<55296||e>56319||b+1===d)return e;b=c.charCodeAt(b+1);return b<56320||b>57343?e:(e-55296)*1024+b+9216}}});
pa("String.fromCodePoint",function(a){return a?a:function(b){for(var c="",d=0;d<arguments.length;d++){var e=Number(arguments[d]);if(e<0||e>1114111||e!==Math.floor(e))throw new RangeError("invalid_code_point "+e);e<=65535?c+=String.fromCharCode(e):(e-=65536,c+=String.fromCharCode(e>>>10&1023|55296),c+=String.fromCharCode(e&1023|56320))}return c}});pa("Object.entries",function(a){return a?a:function(b){var c=[],d;for(d in b)Da(b,d)&&c.push([d,b[d]]);return c}});
pa("String.prototype.endsWith",function(a){return a?a:function(b,c){var d=Ia(this,b,"endsWith");c===void 0&&(c=d.length);c=Math.max(0,Math.min(c|0,d.length));for(var e=b.length;e>0&&c>0;)if(d[--c]!=b[--e])return!1;return e<=0}});pa("Number.isFinite",function(a){return a?a:function(b){return typeof b!=="number"?!1:!isNaN(b)&&b!==Infinity&&b!==-Infinity}});
var Ma=function(a,b,c){a instanceof String&&(a=String(a));for(var d=a.length,e=0;e<d;e++){var f=a[e];if(b.call(c,f,e,a))return{WU:e,OD:f}}return{WU:-1,OD:void 0}};pa("Array.prototype.find",function(a){return a?a:function(b,c){return Ma(this,b,c).OD}});pa("Array.prototype.values",function(a){return a?a:function(){return Ka(this,function(b,c){return c})}});
pa("Promise.prototype.finally",function(a){return a?a:function(b){return this.then(function(c){return Promise.resolve(b()).then(function(){return c})},function(c){return Promise.resolve(b()).then(function(){throw c;})})}});pa("Object.is",function(a){return a?a:function(b,c){return b===c?b!==0||1/b===1/c:b!==b&&c!==c}});
pa("Array.prototype.includes",function(a){return a?a:function(b,c){var d=this;d instanceof String&&(d=String(d));var e=d.length;c=c||0;for(c<0&&(c=Math.max(c+e,0));c<e;c++){var f=d[c];if(f===b||Object.is(f,b))return!0}return!1}});pa("String.prototype.includes",function(a){return a?a:function(b,c){return Ia(this,b,"includes").indexOf(b,c||0)!==-1}});
pa("Array.from",function(a){return a?a:function(b,c,d){c=c!=null?c:function(k){return k};var e=[],f=typeof Symbol!="undefined"&&Symbol.iterator&&b[Symbol.iterator];if(typeof f=="function"){b=f.call(b);for(var h=0;!(f=b.next()).done;)e.push(c.call(d,f.value,h++))}else for(f=b.length,h=0;h<f;h++)e.push(c.call(d,b[h],h));return e}});pa("Object.values",function(a){return a?a:function(b){var c=[],d;for(d in b)Da(b,d)&&c.push(b[d]);return c}});
pa("Array.prototype.flat",function(a){return a?a:function(b){b=b===void 0?1:b;var c=[];Array.prototype.forEach.call(this,function(d){Array.isArray(d)&&b>0?(d=Array.prototype.flat.call(d,b-1),c.push.apply(c,d)):c.push(d)});return c}});pa("Number.MAX_SAFE_INTEGER",function(){return 9007199254740991});pa("Number.MIN_SAFE_INTEGER",function(){return-9007199254740991});pa("Number.isInteger",function(a){return a?a:function(b){return Number.isFinite(b)?b===Math.floor(b):!1}});
pa("Number.isSafeInteger",function(a){return a?a:function(b){return Number.isInteger(b)&&Math.abs(b)<=Number.MAX_SAFE_INTEGER}});pa("Array.prototype.flatMap",function(a){return a?a:function(b,c){var d=[];Array.prototype.forEach.call(this,function(e,f){e=b.call(c,e,f,this);Array.isArray(e)?d.push.apply(d,e):d.push(e)});return d}});pa("Math.imul",function(a){return a?a:function(b,c){b=Number(b);c=Number(c);var d=b&65535,e=c&65535;return d*e+((b>>>16&65535)*e+d*(c>>>16&65535)<<16>>>0)|0}});
pa("Math.trunc",function(a){return a?a:function(b){b=Number(b);if(isNaN(b)||b===Infinity||b===-Infinity||b===0)return b;var c=Math.floor(Math.abs(b));return b<0?-c:c}});pa("Number.isNaN",function(a){return a?a:function(b){return typeof b==="number"&&isNaN(b)}});
pa("String.prototype.replaceAll",function(a){return a?a:function(b,c){if(b instanceof RegExp&&!b.global)throw new TypeError("String.prototype.replaceAll called with a non-global RegExp argument.");return b instanceof RegExp?this.replace(b,c):this.replace(new RegExp(String(b).replace(/([-()\[\]{}+?*.$\^|,:#<!\\])/g,"\\$1").replace(/\x08/g,"\\x08"),"g"),c)}});var Na=function(a){a=Math.trunc(a)||0;a<0&&(a+=this.length);if(!(a<0||a>=this.length))return this[a]};
pa("Array.prototype.at",function(a){return a?a:Na});var Pa=function(a){return a?a:Na};pa("Int8Array.prototype.at",Pa);pa("Uint8Array.prototype.at",Pa);pa("Uint8ClampedArray.prototype.at",Pa);pa("Int16Array.prototype.at",Pa);pa("Uint16Array.prototype.at",Pa);pa("Int32Array.prototype.at",Pa);pa("Uint32Array.prototype.at",Pa);pa("Float32Array.prototype.at",Pa);pa("Float64Array.prototype.at",Pa);pa("String.prototype.at",function(a){return a?a:Na});
pa("Array.prototype.findIndex",function(a){return a?a:function(b,c){return Ma(this,b,c).WU}});_.Ta={};/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
_.Va=_.Va||{};_.Xa=this||self;_.$a=_.Xa._F_toggles||[];_.ab="closure_uid_"+(Math.random()*1E9>>>0);_.bb=function(a,b){var c=Array.prototype.slice.call(arguments,1);return function(){var d=c.slice();d.push.apply(d,arguments);return a.apply(this,d)}};_.t=function(a,b){a=a.split(".");for(var c=_.Xa,d;a.length&&(d=a.shift());)a.length||b===void 0?c=c[d]&&c[d]!==Object.prototype[d]?c[d]:c[d]={}:c[d]=b};
_.eb=function(a,b){function c(){}c.prototype=b.prototype;a.N=b.prototype;a.prototype=new c;a.prototype.constructor=a;a.jt=function(d,e,f){for(var h=Array(arguments.length-2),k=2;k<arguments.length;k++)h[k-2]=arguments[k];return b.prototype[e].apply(d,h)}};_.gb=window.osapi=window.osapi||{};
window.___jsl=window.___jsl||{};
(window.___jsl.cd=window.___jsl.cd||[]).push({gwidget:{parsetags:"explicit"},appsapi:{plus_one_service:"/plus/v1"},csi:{rate:.01},poshare:{hangoutContactPickerServer:"https://plus.google.com"},gappsutil:{required_scopes:["https://www.googleapis.com/auth/plus.me","https://www.googleapis.com/auth/plus.people.recommended"],display_on_page_ready:!1},appsutil:{required_scopes:["https://www.googleapis.com/auth/plus.me","https://www.googleapis.com/auth/plus.people.recommended"],display_on_page_ready:!1},
"oauth-flow":{authUrl:"https://accounts.google.com/o/oauth2/auth",proxyUrl:"https://accounts.google.com/o/oauth2/postmessageRelay",redirectUri:"postmessage"},iframes:{sharebox:{params:{json:"&"},url:":socialhost:/:session_prefix:_/sharebox/dialog"},plus:{url:":socialhost:/:session_prefix:_/widget/render/badge?usegapi=1"},":socialhost:":"https://apis.google.com",":im_socialhost:":"https://plus.googleapis.com",domains_suggest:{url:"https://domains.google.com/suggest/flow"},card:{params:{s:"#",userid:"&"},
url:":socialhost:/:session_prefix:_/hovercard/internalcard"},":signuphost:":"https://plus.google.com",":gplus_url:":"https://plus.google.com",plusone:{url:":socialhost:/:session_prefix:_/+1/fastbutton?usegapi=1"},plus_share:{url:":socialhost:/:session_prefix:_/+1/sharebutton?plusShare=true&usegapi=1"},plus_circle:{url:":socialhost:/:session_prefix:_/widget/plus/circle?usegapi=1"},plus_followers:{url:":socialhost:/_/im/_/widget/render/plus/followers?usegapi=1"},configurator:{url:":socialhost:/:session_prefix:_/plusbuttonconfigurator?usegapi=1"},
appcirclepicker:{url:":socialhost:/:session_prefix:_/widget/render/appcirclepicker"},page:{url:":socialhost:/:session_prefix:_/widget/render/page?usegapi=1"},person:{url:":socialhost:/:session_prefix:_/widget/render/person?usegapi=1"},community:{url:":ctx_socialhost:/:session_prefix::im_prefix:_/widget/render/community?usegapi=1"},follow:{url:":socialhost:/:session_prefix:_/widget/render/follow?usegapi=1"},commentcount:{url:":socialhost:/:session_prefix:_/widget/render/commentcount?usegapi=1"},comments:{url:":socialhost:/:session_prefix:_/widget/render/comments?usegapi=1"},
blogger:{url:":socialhost:/:session_prefix:_/widget/render/blogger?usegapi=1"},youtube:{url:":socialhost:/:session_prefix:_/widget/render/youtube?usegapi=1"},reportabuse:{url:":socialhost:/:session_prefix:_/widget/render/reportabuse?usegapi=1"},additnow:{url:":socialhost:/additnow/additnow.html"},appfinder:{url:"https://workspace.google.com/:session_prefix:marketplace/appfinder?usegapi=1"},":source:":"1p"},poclient:{update_session:"google.updateSessionCallback"},"googleapis.config":{rpc:"/rpc",root:"https://content.googleapis.com",
"root-1p":"https://clients6.google.com",useGapiForXd3:!0,xd3:"/static/proxy.html",auth:{useInterimAuth:!1}},report:{apis:["iframes\\..*","gadgets\\..*","gapi\\.appcirclepicker\\..*","gapi\\.client\\..*"],rate:1E-4},client:{perApiBatch:!0},gen204logger:{interval:3E4,rate:.001,batch:!1}});
var ob;_.jb=function(a){return function(){return _.hb[a].apply(this,arguments)}};_.lb=function(a,b){if(Error.captureStackTrace)Error.captureStackTrace(this,_.lb);else{var c=Error().stack;c&&(this.stack=c)}a&&(this.message=String(a));b!==void 0&&(this.cause=b);this.gZ=!0};ob=function(a,b){a=a.split("%s");for(var c="",d=a.length-1,e=0;e<d;e++)c+=a[e]+(e<b.length?b[e]:"%s");_.lb.call(this,c+a[d])};_.hb=[];_.eb(_.lb,Error);_.lb.prototype.name="CustomError";_.eb(ob,_.lb);ob.prototype.name="AssertionError";
var xb,yb,zb;_.pb=function(a,b){return _.hb[a]=b};_.rb=function(a,b){throw Error(b===void 0?"unexpected value "+a+"!":b);};_.tb=function(a,b){return(0,_.sb)(a,b)>=0};_.ub=function(a){var b=[],c=0,d;for(d in a)b[c++]=a[d];return b};_.vb=function(a){var b=typeof a;return b=="object"&&a!=null||b=="function"};
_.y=function(a,b){a.prototype=(0,_.ua)(b.prototype);a.prototype.constructor=a;if(_.za)(0,_.za)(a,b);else for(var c in b)if(c!="prototype")if(Object.defineProperties){var d=Object.getOwnPropertyDescriptor(b,c);d&&Object.defineProperty(a,c,d)}else a[c]=b[c];a.N=b.prototype};_.wb=function(a,b){a=a.split(".");b=b||_.Xa;for(var c=0;c<a.length;c++)if(b=b[a[c]],b==null)return null;return b};xb=function(a){var b=_.wb("WIZ_global_data.oxN3nb");a=b&&b[a];return a!=null?a:!1};
yb=function(a,b,c){return a.call.apply(a.bind,arguments)};zb=function(a,b,c){if(!a)throw Error();if(arguments.length>2){var d=Array.prototype.slice.call(arguments,2);return function(){var e=Array.prototype.slice.call(arguments);Array.prototype.unshift.apply(e,d);return a.apply(b,e)}}return function(){return a.apply(b,arguments)}};_.z=function(a,b,c){_.z=Function.prototype.bind&&Function.prototype.bind.toString().indexOf("native code")!=-1?yb:zb;return _.z.apply(null,arguments)};_.sb=Array.prototype.indexOf?function(a,b){return Array.prototype.indexOf.call(a,b,void 0)}:function(a,b){if(typeof a==="string")return typeof b!=="string"||b.length!=1?-1:a.indexOf(b,0);for(var c=0;c<a.length;c++)if(c in a&&a[c]===b)return c;return-1};
_.Ab=Array.prototype.lastIndexOf?function(a,b){return Array.prototype.lastIndexOf.call(a,b,a.length-1)}:function(a,b){var c=a.length-1;c<0&&(c=Math.max(0,a.length+c));if(typeof a==="string")return typeof b!=="string"||b.length!=1?-1:a.lastIndexOf(b,c);for(;c>=0;c--)if(c in a&&a[c]===b)return c;return-1};_.Bb=Array.prototype.forEach?function(a,b,c){Array.prototype.forEach.call(a,b,c)}:function(a,b,c){for(var d=a.length,e=typeof a==="string"?a.split(""):a,f=0;f<d;f++)f in e&&b.call(c,e[f],f,a)};
_.Gb=Array.prototype.filter?function(a,b){return Array.prototype.filter.call(a,b,void 0)}:function(a,b){for(var c=a.length,d=[],e=0,f=typeof a==="string"?a.split(""):a,h=0;h<c;h++)if(h in f){var k=f[h];b.call(void 0,k,h,a)&&(d[e++]=k)}return d};_.Ib=Array.prototype.map?function(a,b,c){return Array.prototype.map.call(a,b,c)}:function(a,b,c){for(var d=a.length,e=Array(d),f=typeof a==="string"?a.split(""):a,h=0;h<d;h++)h in f&&(e[h]=b.call(c,f[h],h,a));return e};
_.Jb=Array.prototype.some?function(a,b,c){return Array.prototype.some.call(a,b,c)}:function(a,b,c){for(var d=a.length,e=typeof a==="string"?a.split(""):a,f=0;f<d;f++)if(f in e&&b.call(c,e[f],f,a))return!0;return!1};_.Nb=Array.prototype.every?function(a,b,c){return Array.prototype.every.call(a,b,c)}:function(a,b,c){for(var d=a.length,e=typeof a==="string"?a.split(""):a,f=0;f<d;f++)if(f in e&&!b.call(c,e[f],f,a))return!1;return!0};var Ob=!!(_.$a[0]&1024),Pb=!!(_.$a[0]&2048),Qb=!!(_.$a[0]&8192);_.Rb=Ob?Pb:xb(610401301);_.Sb=Ob?Qb:xb(651175828);_.Ub=function(a){_.Ub[" "](a);return a};_.Ub[" "]=function(){};
/*

 Copyright Google LLC
 SPDX-License-Identifier: Apache-2.0
*/
var Zb,ac,nc,zc,Jc,Wc,ed;_.Vb=function(a){var b=a.length;if(b>0){for(var c=Array(b),d=0;d<b;d++)c[d]=a[d];return c}return[]};_.Wb=function(a,b,c){for(var d in a)b.call(c,a[d],d,a)};Zb=function(){var a=null;if(!Yb)return a;try{var b=function(c){return c};a=Yb.createPolicy("gapi#html",{createHTML:b,createScript:b,createScriptURL:b})}catch(c){}return a};ac=function(){$b===void 0&&($b=Zb());return $b};_.cc=function(a){var b=ac();a=b?b.createHTML(a):a;return new _.bc(a)};
_.dc=function(a){if(a instanceof _.bc)return a.EY;throw Error("j");};_.fc=function(a){return new _.ec(a)};_.hc=function(a){var b=ac();a=b?b.createScriptURL(a):a;return new _.gc(a)};_.ic=function(a){if(a instanceof _.gc)return a.FY;throw Error("j");};_.kc=function(a){return a instanceof _.jc};_.lc=function(a){if(_.kc(a))return a.HY;throw Error("j");};nc=function(a){return new _.mc(function(b){return b.substr(0,a.length+1).toLowerCase()===a+":"})};_.qc=function(a){if(oc.test(a))return a};
_.rc=function(a){return a instanceof _.jc?_.lc(a):_.qc(a)};_.sc=function(a,b){if(a instanceof _.bc)return a;a=String(a).replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;");if(b==null?0:b.Ura)a=a.replace(/(^|[\r\n\t ]) /g,"$1&#160;");if(b==null?0:b.bea)a=a.replace(/(\r\n|\n|\r)/g,"<br>");if(b==null?0:b.Vra)a=a.replace(/(\t+)/g,'<span style="white-space:pre">$1</span>');return _.cc(a)};
_.uc=function(a){var b=_.tc.apply(1,arguments);if(b.length===0)return _.hc(a[0]);for(var c=a[0],d=0;d<b.length;d++)c+=encodeURIComponent(b[d])+a[d+1];return _.hc(c)};_.vc=function(a,b){return a.lastIndexOf(b,0)==0};_.wc=function(a){return/^[\s\xa0]*$/.test(a)};_.xc=function(a,b){return a.indexOf(b)!=-1};
_.Ac=function(a,b){var c=0;a=(0,_.yc)(String(a)).split(".");b=(0,_.yc)(String(b)).split(".");for(var d=Math.max(a.length,b.length),e=0;c==0&&e<d;e++){var f=a[e]||"",h=b[e]||"";do{f=/(\d*)(\D*)(.*)/.exec(f)||["","","",""];h=/(\d*)(\D*)(.*)/.exec(h)||["","","",""];if(f[0].length==0&&h[0].length==0)break;c=zc(f[1].length==0?0:parseInt(f[1],10),h[1].length==0?0:parseInt(h[1],10))||zc(f[2].length==0,h[2].length==0)||zc(f[2],h[2]);f=f[3];h=h[3]}while(c==0)}return c};
zc=function(a,b){return a<b?-1:a>b?1:0};_.Bc=function(a,b){b=_.rc(b);b!==void 0&&(a.href=b)};_.Dc=function(a,b,c,d){b=_.rc(b);return b!==void 0?a.open(b,c,d):null};_.Ec=function(a,b){b=b===void 0?document:b;var c,d;b=(d=(c=b).querySelector)==null?void 0:d.call(c,a+"[nonce]");return b==null?"":b.nonce||b.getAttribute("nonce")||""};_.Fc=function(a,b){if(a.nodeType===1&&/^(script|style)$/i.test(a.tagName))throw Error("j");a.innerHTML=_.dc(b)};
_.Gc=function(){var a=_.Xa.navigator;return a&&(a=a.userAgent)?a:""};Jc=function(a){if(!_.Rb||!_.Hc)return!1;for(var b=0;b<_.Hc.brands.length;b++){var c=_.Hc.brands[b].brand;if(c&&_.xc(c,a))return!0}return!1};_.Kc=function(a){return _.xc(_.Gc(),a)};_.Lc=function(a){for(var b=RegExp("([A-Z][\\w ]+)/([^\\s]+)\\s*(?:\\((.*?)\\))?","g"),c=[],d;d=b.exec(a);)c.push([d[1],d[2],d[3]||void 0]);return c};_.Mc=function(){return _.Rb?!!_.Hc&&_.Hc.brands.length>0:!1};_.Nc=function(){return _.Mc()?!1:_.Kc("Opera")};
_.Oc=function(){return _.Mc()?!1:_.Kc("Trident")||_.Kc("MSIE")};_.Pc=function(){return _.Mc()?!1:_.Kc("Edge")};_.Qc=function(){return _.Mc()?Jc("Microsoft Edge"):_.Kc("Edg/")};_.Sc=function(){return _.Mc()?Jc("Opera"):_.Kc("OPR")};_.Tc=function(){return _.Mc()?Jc("Chromium"):(_.Kc("Chrome")||_.Kc("CriOS"))&&!_.Pc()||_.Kc("Silk")};_.Uc=function(a){var b={};a.forEach(function(c){b[c[0]]=c[1]});return function(c){return b[c.find(function(d){return d in b})]||""}};
_.Vc=function(a){var b=/rv: *([\d\.]*)/.exec(a);if(b&&b[1])return b[1];b="";var c=/MSIE +([\d\.]+)/.exec(a);if(c&&c[1])if(a=/Trident\/(\d.\d)/.exec(a),c[1]=="7.0")if(a&&a[1])switch(a[1]){case "4.0":b="8.0";break;case "5.0":b="9.0";break;case "6.0":b="10.0";break;case "7.0":b="11.0"}else b="7.0";else b=c[1];return b};Wc=function(){return _.Rb?!!_.Hc&&!!_.Hc.platform:!1};_.Xc=function(){return Wc()?_.Hc.platform==="Android":_.Kc("Android")};_.Yc=function(){return _.Kc("iPhone")&&!_.Kc("iPod")&&!_.Kc("iPad")};
_.Zc=function(){return _.Yc()||_.Kc("iPad")||_.Kc("iPod")};_.$c=function(){return Wc()?_.Hc.platform==="macOS":_.Kc("Macintosh")};_.ad=function(){return Wc()?_.Hc.platform==="Windows":_.Kc("Windows")};_.bd=function(){return Wc()?_.Hc.platform==="Chrome OS":_.Kc("CrOS")};_.cd=function(a,b){a.raw=b;Object.freeze&&(Object.freeze(a),Object.freeze(b));return a};_.dd=function(a){return _.cd(a,a)};_.tc=function(){for(var a=Number(this),b=[],c=a;c<arguments.length;c++)b[c-a]=arguments[c];return b};
_.gd=function(a){var b=typeof a;return b!="object"?b:a?Array.isArray(a)?"array":b:"null"};_.hd=function(a){var b=_.gd(a);return b=="array"||b=="object"&&typeof a.length=="number"};_.id=function(){return Date.now()};var jd=globalThis.trustedTypes,Yb=jd,$b;_.bc=function(a){this.EY=a};_.bc.prototype.toString=function(){return this.EY+""};_.kd=function(){return new _.bc(jd?jd.emptyHTML:"")}();_.ec=function(a){this.GY=a};_.ec.prototype.toString=function(){return this.GY};_.gc=function(a){this.FY=a};_.gc.prototype.toString=function(){return this.FY+""};_.jc=function(a){this.HY=a};_.jc.prototype.toString=function(){return this.HY};_.ld=new _.jc("about:invalid#zClosurez");var oc;_.mc=function(a){this.yj=a};_.md=[nc("data"),nc("http"),nc("https"),nc("mailto"),nc("ftp"),new _.mc(function(a){return/^[^:]*([/?#]|$)/.test(a)})];_.nd=function(){return typeof URL==="function"}();oc=/^\s*(?!javascript:)(?:[\w+.-]+:|[^:/?#]*(?:[/?#]|$))/i;_.od=function(a,b){this.width=a;this.height=b};_.pd=function(a,b){return a==b?!0:a&&b?a.width==b.width&&a.height==b.height:!1};_.g=_.od.prototype;_.g.clone=function(){return new _.od(this.width,this.height)};_.g.Vx=function(){return this.width*this.height};_.g.aspectRatio=function(){return this.width/this.height};_.g.isEmpty=function(){return!this.Vx()};_.g.ceil=function(){this.width=Math.ceil(this.width);this.height=Math.ceil(this.height);return this};
_.g.floor=function(){this.width=Math.floor(this.width);this.height=Math.floor(this.height);return this};_.g.round=function(){this.width=Math.round(this.width);this.height=Math.round(this.height);return this};_.g.scale=function(a,b){this.width*=a;this.height*=typeof b==="number"?b:a;return this};_.yc=String.prototype.trim?function(a){return a.trim()}:function(a){return/^[\s\xa0]*([\s\S]*?)[\s\xa0]*$/.exec(a)[1]};_.qd=String.prototype.repeat?function(a,b){return a.repeat(b)}:function(a,b){return Array(b+1).join(a)};_.rd=Math.random()*2147483648|0;var sd;sd=_.Xa.navigator;_.Hc=sd?sd.userAgentData||null:null;var Kd,Ld,Td;_.td=_.Nc();_.vd=_.Oc();_.wd=_.Kc("Edge");_.xd=_.wd||_.vd;_.yd=_.Kc("Gecko")&&!(_.xc(_.Gc().toLowerCase(),"webkit")&&!_.Kc("Edge"))&&!(_.Kc("Trident")||_.Kc("MSIE"))&&!_.Kc("Edge");_.zd=_.xc(_.Gc().toLowerCase(),"webkit")&&!_.Kc("Edge");_.Ad=_.zd&&_.Kc("Mobile");_.Bd=_.$c();_.Cd=_.ad();_.Dd=(Wc()?_.Hc.platform==="Linux":_.Kc("Linux"))||_.bd();_.Ed=_.Xc();_.Fd=_.Yc();_.Hd=_.Kc("iPad");_.Id=_.Kc("iPod");_.Jd=_.Zc();Kd=function(){var a=_.Xa.document;return a?a.documentMode:void 0};
a:{var Md="",Nd=function(){var a=_.Gc();if(_.yd)return/rv:([^\);]+)(\)|;)/.exec(a);if(_.wd)return/Edge\/([\d\.]+)/.exec(a);if(_.vd)return/\b(?:MSIE|rv)[: ]([^\);]+)(\)|;)/.exec(a);if(_.zd)return/WebKit\/(\S+)/.exec(a);if(_.td)return/(?:Version)[ \/]?(\S+)/.exec(a)}();Nd&&(Md=Nd?Nd[1]:"");if(_.vd){var Od=Kd();if(Od!=null&&Od>parseFloat(Md)){Ld=String(Od);break a}}Ld=Md}_.Sd=Ld;if(_.Xa.document&&_.vd){var Ud=Kd();Td=Ud?Ud:parseInt(_.Sd,10)||void 0}else Td=void 0;_.Vd=Td;var ae,he,ge;_.Yd=function(a){return a?new _.Wd(_.Xd(a)):ed||(ed=new _.Wd)};_.Zd=function(a,b){return typeof b==="string"?a.getElementById(b):b};_.$d=function(a,b,c,d){a=d||a;return(b=b&&b!="*"?String(b).toUpperCase():"")||c?a.querySelectorAll(b+(c?"."+c:"")):a.getElementsByTagName("*")};
_.be=function(a,b){_.Wb(b,function(c,d){d=="style"?a.style.cssText=c:d=="class"?a.className=c:d=="for"?a.htmlFor=c:ae.hasOwnProperty(d)?a.setAttribute(ae[d],c):_.vc(d,"aria-")||_.vc(d,"data-")?a.setAttribute(d,c):a[d]=c})};ae={cellpadding:"cellPadding",cellspacing:"cellSpacing",colspan:"colSpan",frameborder:"frameBorder",height:"height",maxlength:"maxLength",nonce:"nonce",role:"role",rowspan:"rowSpan",type:"type",usemap:"useMap",valign:"vAlign",width:"width"};_.de=function(a){return _.ce(a||window)};
_.ce=function(a){a=a.document;a=_.ee(a)?a.documentElement:a.body;return new _.od(a.clientWidth,a.clientHeight)};_.fe=function(a){return a?a.defaultView:window};_.ie=function(a,b){var c=b[1],d=ge(a,String(b[0]));c&&(typeof c==="string"?d.className=c:Array.isArray(c)?d.className=c.join(" "):_.be(d,c));b.length>2&&he(a,d,b,2);return d};
he=function(a,b,c,d){function e(k){k&&b.appendChild(typeof k==="string"?a.createTextNode(k):k)}for(;d<c.length;d++){var f=c[d];if(!_.hd(f)||_.vb(f)&&f.nodeType>0)e(f);else{a:{if(f&&typeof f.length=="number"){if(_.vb(f)){var h=typeof f.item=="function"||typeof f.item=="string";break a}if(typeof f==="function"){h=typeof f.item=="function";break a}}h=!1}_.Bb(h?_.Vb(f):f,e)}}};_.je=function(a){return ge(document,a)};
ge=function(a,b){b=String(b);a.contentType==="application/xhtml+xml"&&(b=b.toLowerCase());return a.createElement(b)};_.ee=function(a){return a.compatMode=="CSS1Compat"};_.ke=function(a){if(a.nodeType!=1)return!1;switch(a.tagName){case "APPLET":case "AREA":case "BASE":case "BR":case "COL":case "COMMAND":case "EMBED":case "FRAME":case "HR":case "IMG":case "INPUT":case "IFRAME":case "ISINDEX":case "KEYGEN":case "LINK":case "NOFRAMES":case "NOSCRIPT":case "META":case "OBJECT":case "PARAM":case "SCRIPT":case "SOURCE":case "STYLE":case "TRACK":case "WBR":return!1}return!0};
_.le=function(a,b){he(_.Xd(a),a,arguments,1)};_.me=function(a){for(var b;b=a.firstChild;)a.removeChild(b)};_.ne=function(a,b){b.parentNode&&b.parentNode.insertBefore(a,b)};_.oe=function(a){return a&&a.parentNode?a.parentNode.removeChild(a):null};_.pe=function(a){return a.children!=void 0?a.children:Array.prototype.filter.call(a.childNodes,function(b){return b.nodeType==1})};_.qe=function(a){return _.vb(a)&&a.nodeType==1};
_.re=function(a,b){if(!a||!b)return!1;if(a.contains&&b.nodeType==1)return a==b||a.contains(b);if(typeof a.compareDocumentPosition!="undefined")return a==b||!!(a.compareDocumentPosition(b)&16);for(;b&&a!=b;)b=b.parentNode;return b==a};_.Xd=function(a){return a.nodeType==9?a:a.ownerDocument||a.document};
_.se=function(a,b){if("textContent"in a)a.textContent=b;else if(a.nodeType==3)a.data=String(b);else if(a.firstChild&&a.firstChild.nodeType==3){for(;a.lastChild!=a.firstChild;)a.removeChild(a.lastChild);a.firstChild.data=String(b)}else _.me(a),a.appendChild(_.Xd(a).createTextNode(String(b)))};_.Wd=function(a){this.Cc=a||_.Xa.document||document};_.g=_.Wd.prototype;_.g.Ia=_.Yd;_.g.qL=_.jb(0);_.g.tb=function(){return this.Cc};_.g.O=_.jb(1);_.g.getElementsByTagName=function(a,b){return(b||this.Cc).getElementsByTagName(String(a))};
_.g.pH=_.jb(2);_.g.wa=function(a,b,c){return _.ie(this.Cc,arguments)};_.g.createElement=function(a){return ge(this.Cc,a)};_.g.createTextNode=function(a){return this.Cc.createTextNode(String(a))};_.g.getWindow=function(){return this.Cc.defaultView};_.g.appendChild=function(a,b){a.appendChild(b)};_.g.append=_.le;_.g.canHaveChildren=_.ke;_.g.re=_.me;_.g.tV=_.ne;_.g.removeNode=_.oe;_.g.AG=_.pe;_.g.isElement=_.qe;_.g.contains=_.re;_.g.SG=_.Xd;_.g.wj=_.jb(3);
/*
 gapi.loader.OBJECT_CREATE_TEST_OVERRIDE &&*/
_.te=function(a){return a===null?"null":a===void 0?"undefined":a};_.ue=window;_.ve=document;_.we=_.ue.location;_.xe=/\[native code\]/;_.ye=function(a,b,c){return a[b]=a[b]||c};_.ze=function(){var a;if((a=Object.create)&&_.xe.test(a))a=a(null);else{a={};for(var b in a)a[b]=void 0}return a};_.Ae=function(a,b){return Object.prototype.hasOwnProperty.call(a,b)};_.Be=function(a,b){a=a||{};for(var c in a)_.Ae(a,c)&&(b[c]=a[c])};_.Ce=_.ye(_.ue,"gapi",{});_.De=function(a,b,c){var d=new RegExp("([#].*&|[#])"+b+"=([^&#]*)","g");b=new RegExp("([?#].*&|[?#])"+b+"=([^&#]*)","g");if(a=a&&(d.exec(a)||b.exec(a)))try{c=decodeURIComponent(a[2])}catch(e){}return c};_.Ee=new RegExp(/^/.source+/([a-zA-Z][-+.a-zA-Z0-9]*:)?/.source+/(\/\/[^\/?#]*)?/.source+/([^?#]*)?/.source+/(\?([^#]*))?/.source+/(#((#|[^#])*))?/.source+/$/.source);_.Fe=new RegExp(/(%([^0-9a-fA-F%]|[0-9a-fA-F]([^0-9a-fA-F%])?)?)*/.source+/%($|[^0-9a-fA-F]|[0-9a-fA-F]($|[^0-9a-fA-F]))/.source,"g");
_.Ge=new RegExp(/\/?\??#?/.source+"("+/[\/?#]/i.source+"|"+/[\uD800-\uDBFF]/i.source+"|"+/%[c-f][0-9a-f](%[89ab][0-9a-f]){0,2}(%[89ab]?)?/i.source+"|"+/%[0-9a-f]?/i.source+")$","i");_.Ie=function(a,b,c){_.He(a,b,c,"add","at")};_.He=function(a,b,c,d,e){if(a[d+"EventListener"])a[d+"EventListener"](b,c,!1);else if(a[e+"tachEvent"])a[e+"tachEvent"]("on"+b,c)};_.Je={};_.Je=_.ye(_.ue,"___jsl",_.ze());_.ye(_.Je,"I",0);_.ye(_.Je,"hel",10);var Ke,Le,Me,Ne,Re,Oe,Pe,Se,Te;Ke=function(a){var b=window.___jsl=window.___jsl||{};b[a]=b[a]||[];return b[a]};Le=function(a){var b=window.___jsl=window.___jsl||{};b.cfg=!a&&b.cfg||{};return b.cfg};Me=function(a){return typeof a==="object"&&/\[native code\]/.test(a.push)};
Ne=function(a,b,c){if(b&&typeof b==="object")for(var d in b)!Object.prototype.hasOwnProperty.call(b,d)||c&&d==="___goc"&&typeof b[d]==="undefined"||(a[d]&&b[d]&&typeof a[d]==="object"&&typeof b[d]==="object"&&!Me(a[d])&&!Me(b[d])?Ne(a[d],b[d]):b[d]&&typeof b[d]==="object"?(a[d]=Me(b[d])?[]:{},Ne(a[d],b[d])):a[d]=b[d])};
Re=function(a,b){if(a&&!/^\s+$/.test(a)){for(;a.charCodeAt(a.length-1)==0;)a=a.substring(0,a.length-1);var c=a,d=Ke("dm");d.push(20);try{var e=window.JSON.parse(a)}catch(h){}if(typeof e==="object")return d.push(21),e;try{e=window.JSON.parse("{"+a+"}")}catch(h){}if(typeof e==="object")return d.push(22),e;a=a.replace(RegExp("([^\"',{}\\s]+?)\\s*:\\s*(.*?)[,}\\s]","g"),function(h,k,l){l=l.startsWith('"')?"%DOUBLE_QUOTE%"+l.substring(1):l;l=l.endsWith('"')?l.slice(0,-1)+"%DOUBLE_QUOTE%":l;return"%DOUBLE_QUOTE%"+
k+"%DOUBLE_QUOTE%:"+l});a=a.replace(/\\'/g,"%SINGLE_QUOTE%");a=a.replace(/"/g,'\\"');a=a.replace(/'/g,'"');a=a.replace(/%SINGLE_QUOTE%/g,"'");a=a.replace(/%DOUBLE_QUOTE%/g,'"');try{e=window.JSON.parse(a)}catch(h){}if(typeof e==="object")return d.push(23),e;try{e=window.JSON.parse("{"+a+"}")}catch(h){}if(typeof e==="object")return d.push(24),e;a=document.getElementsByTagName("script")||[];var f;a.length>0&&(f=a[0].nonce||a[0].getAttribute("nonce"));if(f&&f===b||!f&&Oe())if(e=Pe(c),d.push(25),typeof e===
"object")return e;return{}}};Oe=function(){var a=window.location.hostname;return a?/(^|\.)(2mdn|ampproject|android|appspot|blogger|blogspot|chrome|chromium|doubleclick|gcpnode|ggpht|gmail|google|google-analytics|googleadservices|googleapis|googleapis-cn|googleoptimize|googlers|googlesource|googlesyndication|googletagmanager|googletagservices|googleusercontent|googlevideo|gstatic|tiltbrush|waze|withgoogle|youtube|ytimg)(\.com?|\.net|\.org)?(\.[a-z][a-z]|\.cat)?$/.test(a):!1};
Pe=function(a){try{var b=(new Function("return ("+a+"\n)"))()}catch(c){}if(typeof b==="object")return b;try{b=(new Function("return ({"+a+"\n})"))()}catch(c){}return b};Se=function(a,b){var c={___goc:void 0};a.length&&a[a.length-1]&&Object.hasOwnProperty.call(a[a.length-1],"___goc")&&typeof a[a.length-1].___goc==="undefined"&&(c=a.pop());Ne(c,b);a.push(c)};
Te=function(a){Le(!0);var b=window.___gcfg,c=Ke("cu"),d=window.___gu;b&&b!==d&&(Se(c,b),window.___gu=b);b=Ke("cu");var e=document.getElementsByTagName("script")||[];d=[];var f=[];f.push.apply(f,Ke("us"));for(var h=0;h<e.length;++h)for(var k=e[h],l=0;l<f.length;++l)k.src&&k.src.indexOf(f[l])==0&&d.push(k);d.length==0&&e.length>0&&e[e.length-1].src&&d.push(e[e.length-1]);for(e=0;e<d.length;++e)d[e].getAttribute("gapi_processed")||(d[e].setAttribute("gapi_processed",!0),(f=d[e])?(h=f.nodeType,f=h==3||
h==4?f.nodeValue:f.textContent||""):f=void 0,h=d[e].nonce||d[e].getAttribute("nonce"),(f=Re(f,h))&&b.push(f));a&&Se(c,a);d=Ke("cd");a=0;for(b=d.length;a<b;++a)Ne(Le(),d[a],!0);d=Ke("ci");a=0;for(b=d.length;a<b;++a)Ne(Le(),d[a],!0);a=0;for(b=c.length;a<b;++a)Ne(Le(),c[a],!0)};_.Ue=function(a,b){var c=Le();if(!a)return c;a=a.split("/");for(var d=0,e=a.length;c&&typeof c==="object"&&d<e;++d)c=c[a[d]];return d===a.length&&c!==void 0?c:b};
_.Ve=function(a,b){var c;if(typeof a==="string"){var d=c={};a=a.split("/");for(var e=0,f=a.length;e<f-1;++e){var h={};d=d[a[e]]=h}d[a[e]]=b}else c=a;Te(c)};var We=function(){var a=window.__GOOGLEAPIS;a&&(a.googleapis&&!a["googleapis.config"]&&(a["googleapis.config"]=a.googleapis),_.ye(_.Je,"ci",[]).push(a),window.__GOOGLEAPIS=void 0)};We&&We();Te();_.t("gapi.config.get",_.Ue);_.t("gapi.config.update",_.Ve);
_.Xe=function(a){a=_.te(a);return _.cc(a)};
_.Gg=window.googleapis&&window.googleapis.server||{};
_.Ye=_.Ye||{};
_.Ye=_.Ye||{};
(function(){function a(c){var d=typeof c==="undefined";if(b!==null&&d)return b;var e={};c=c||window.location.href;var f=c.indexOf("?"),h=c.indexOf("#");c=(h===-1?c.substr(f+1):[c.substr(f+1,h-f-1),"&",c.substr(h+1)].join("")).split("&");f=window.decodeURIComponent?decodeURIComponent:unescape;h=0;for(var k=c.length;h<k;++h){var l=c[h].indexOf("=");if(l!==-1){var m=c[h].substring(0,l);l=c[h].substring(l+1);l=l.replace(/\+/g," ");try{e[m]=f(l)}catch(n){}}}d&&(b=e);return e}var b=null;_.Ye.Sg=a;a()})();_.t("gadgets.util.getUrlParameters",_.Ye.Sg);
_.bf=function(){var a=window.gadgets&&window.gadgets.config&&window.gadgets.config.get;a&&_.Ve(a());return{register:function(b,c,d){d&&d(_.Ue())},get:function(b){return _.Ue(b)},update:function(b,c){if(c)throw"Config replacement is not supported";_.Ve(b)},init:function(){}}}();_.t("gadgets.config.register",_.bf.register);_.t("gadgets.config.get",_.bf.get);_.t("gadgets.config.init",_.bf.init);_.t("gadgets.config.update",_.bf.update);
var cf,df,ef,ff,gf,hf,jf,kf,lf,nf,pf,qf,rf,sf,tf,uf,vf,wf,xf,yf,zf,Af,Bf,Df,Gf,Hf,If,Jf,Kf,Lf,Mf,Pf,Qf;ef=void 0;ff=function(a){try{return _.Xa.JSON.parse.call(_.Xa.JSON,a)}catch(b){return!1}};gf=function(a){return Object.prototype.toString.call(a)};hf=gf(0);jf=gf(new Date(0));kf=gf(!0);lf=gf("");nf=gf({});pf=gf([]);
qf=function(a,b){if(b)for(var c=0,d=b.length;c<d;++c)if(a===b[c])throw new TypeError("Converting circular structure to JSON");d=typeof a;if(d!=="undefined"){c=Array.prototype.slice.call(b||[],0);c[c.length]=a;b=[];var e=gf(a);if(a!=null&&typeof a.toJSON==="function"&&(Object.prototype.hasOwnProperty.call(a,"toJSON")||(e!==pf||a.constructor!==Array&&a.constructor!==Object)&&(e!==nf||a.constructor!==Array&&a.constructor!==Object)&&e!==lf&&e!==hf&&e!==kf&&e!==jf))return qf(a.toJSON.call(a),c);if(a==
null)b[b.length]="null";else if(e===hf)a=Number(a),isNaN(a)||isNaN(a-a)?a="null":a===-0&&1/a<0&&(a="-0"),b[b.length]=String(a);else if(e===kf)b[b.length]=String(!!Number(a));else{if(e===jf)return qf(a.toISOString.call(a),c);if(e===pf&&gf(a.length)===hf){b[b.length]="[";var f=0;for(d=Number(a.length)>>0;f<d;++f)f&&(b[b.length]=","),b[b.length]=qf(a[f],c)||"null";b[b.length]="]"}else if(e==lf&&gf(a.length)===hf){b[b.length]='"';f=0;for(c=Number(a.length)>>0;f<c;++f)d=String.prototype.charAt.call(a,
f),e=String.prototype.charCodeAt.call(a,f),b[b.length]=d==="\b"?"\\b":d==="\f"?"\\f":d==="\n"?"\\n":d==="\r"?"\\r":d==="\t"?"\\t":d==="\\"||d==='"'?"\\"+d:e<=31?"\\u"+(e+65536).toString(16).substr(1):e>=32&&e<=65535?d:"\ufffd";b[b.length]='"'}else if(d==="object"){b[b.length]="{";d=0;for(f in a)Object.prototype.hasOwnProperty.call(a,f)&&(e=qf(a[f],c),e!==void 0&&(d++&&(b[b.length]=","),b[b.length]=qf(f),b[b.length]=":",b[b.length]=e));b[b.length]="}"}else return}return b.join("")}};rf=/[\0-\x07\x0b\x0e-\x1f]/;
sf=/^([^"]*"([^\\"]|\\.)*")*[^"]*"([^"\\]|\\.)*[\0-\x1f]/;tf=/^([^"]*"([^\\"]|\\.)*")*[^"]*"([^"\\]|\\.)*\\[^\\\/"bfnrtu]/;uf=/^([^"]*"([^\\"]|\\.)*")*[^"]*"([^"\\]|\\.)*\\u([0-9a-fA-F]{0,3}[^0-9a-fA-F])/;vf=/"([^\0-\x1f\\"]|\\[\\\/"bfnrt]|\\u[0-9a-fA-F]{4})*"/g;wf=/-?(0|[1-9][0-9]*)(\.[0-9]+)?([eE][-+]?[0-9]+)?/g;xf=/[ \t\n\r]+/g;yf=/[^"]:/;zf=/""/g;Af=/true|false|null/g;Bf=/00/;Df=/[\{]([^0\}]|0[^:])/;Gf=/(^|\[)[,:]|[,:](\]|\}|[,:]|$)/;Hf=/[^\[,:][\[\{]/;If=/^(\{|\}|\[|\]|,|:|0)+/;Jf=/\u2028/g;
Kf=/\u2029/g;
Lf=function(a){a=String(a);if(rf.test(a)||sf.test(a)||tf.test(a)||uf.test(a))return!1;var b=a.replace(vf,'""');b=b.replace(wf,"0");b=b.replace(xf,"");if(yf.test(b))return!1;b=b.replace(zf,"0");b=b.replace(Af,"0");if(Bf.test(b)||Df.test(b)||Gf.test(b)||Hf.test(b)||!b||(b=b.replace(If,"")))return!1;a=a.replace(Jf,"\\u2028").replace(Kf,"\\u2029");b=void 0;try{b=ef?[ff(a)]:eval("(function (var_args) {\n  return Array.prototype.slice.call(arguments, 0);\n})(\n"+a+"\n)")}catch(c){return!1}return b&&b.length===
1?b[0]:!1};Mf=function(){var a=((_.Xa.document||{}).scripts||[]).length;if((cf===void 0||ef===void 0||df!==a)&&df!==-1){cf=ef=!1;df=-1;try{try{ef=!!_.Xa.JSON&&_.Xa.JSON.stringify.call(_.Xa.JSON,{a:[3,!0,new Date(0)],c:function(){}})==='{"a":[3,true,"1970-01-01T00:00:00.000Z"]}'&&ff("true")===!0&&ff('[{"a":3}]')[0].a===3}catch(b){}cf=ef&&!ff("[00]")&&!ff('"\u0007"')&&!ff('"\\0"')&&!ff('"\\v"')}finally{df=a}}};_.Nf=function(a){if(df===-1)return!1;Mf();return(cf?ff:Lf)(a)};
_.Of=function(a){if(df!==-1)return Mf(),ef?_.Xa.JSON.stringify.call(_.Xa.JSON,a):qf(a)};Pf=!Date.prototype.toISOString||typeof Date.prototype.toISOString!=="function"||(new Date(0)).toISOString()!=="1970-01-01T00:00:00.000Z";
Qf=function(){var a=Date.prototype.getUTCFullYear.call(this);return[a<0?"-"+String(1E6-a).substr(1):a<=9999?String(1E4+a).substr(1):"+"+String(1E6+a).substr(1),"-",String(101+Date.prototype.getUTCMonth.call(this)).substr(1),"-",String(100+Date.prototype.getUTCDate.call(this)).substr(1),"T",String(100+Date.prototype.getUTCHours.call(this)).substr(1),":",String(100+Date.prototype.getUTCMinutes.call(this)).substr(1),":",String(100+Date.prototype.getUTCSeconds.call(this)).substr(1),".",String(1E3+Date.prototype.getUTCMilliseconds.call(this)).substr(1),
"Z"].join("")};Date.prototype.toISOString=Pf?Qf:Date.prototype.toISOString;
_.t("gadgets.json.stringify",_.Of);_.t("gadgets.json.parse",_.Nf);
(function(){function a(e,f){if(!(e<c)&&d)if(e===2&&d.warn)d.warn(f);else if(e===3&&d.error)try{d.error(f)}catch(h){}else d.log&&d.log(f)}var b=function(e){a(1,e)};_.Ze=function(e){a(2,e)};_.$e=function(e){a(3,e)};_.af=function(){};b.INFO=1;b.WARNING=2;b.NONE=4;var c=1,d=window.console?window.console:window.opera?window.opera.postError:void 0;return b})();
_.Ye=_.Ye||{};(function(){var a=[];_.Ye.Zra=function(b){a.push(b)};_.Ye.msa=function(){for(var b=0,c=a.length;b<c;++b)a[b]()}})();
var Rf=function(){this.Fg=window.console};Rf.prototype.log=function(a){this.Fg&&this.Fg.log&&this.Fg.log(a)};Rf.prototype.error=function(a){this.Fg&&(this.Fg.error?this.Fg.error(a):this.Fg.log&&this.Fg.log(a))};Rf.prototype.warn=function(a){this.Fg&&(this.Fg.warn?this.Fg.warn(a):this.Fg.log&&this.Fg.log(a))};Rf.prototype.debug=function(){};_.Sf=new Rf;
_.Tf=function(){var a=_.ve.readyState;return a==="complete"||a==="interactive"&&navigator.userAgent.indexOf("MSIE")==-1};_.Uf=function(a){if(_.Tf())a();else{var b=!1,c=function(){if(!b)return b=!0,a.apply(this,arguments)};_.ue.addEventListener?(_.ue.addEventListener("load",c,!1),_.ue.addEventListener("DOMContentLoaded",c,!1)):_.ue.attachEvent&&(_.ue.attachEvent("onreadystatechange",function(){_.Tf()&&c.apply(this,arguments)}),_.ue.attachEvent("onload",c))}};
_.Vf=function(a,b){var c=_.ye(_.Je,"watt",_.ze());_.ye(c,a,b)};_.De(_.ue.location.href,"rpctoken")&&_.Ie(_.ve,"unload",function(){});var Wf=Wf||{};Wf.vZ=null;Wf.lX=null;Wf.AA=null;Wf.frameElement=null;Wf=Wf||{};
Wf.QN||(Wf.QN=function(){function a(f,h,k){typeof window.addEventListener!="undefined"?window.addEventListener(f,h,k):typeof window.attachEvent!="undefined"&&window.attachEvent("on"+f,h);f==="message"&&(window.___jsl=window.___jsl||{},f=window.___jsl,f.RPMQ=f.RPMQ||[],f.RPMQ.push(h))}function b(f){var h=_.Nf(f.data);if(h&&h.f){_.af();var k=_.Xf.eo(h.f);e&&(typeof f.origin!=="undefined"?f.origin!==k:f.domain!==/^.+:\/\/([^:]+).*/.exec(k)[1])?_.$e("Invalid rpc message origin. "+k+" vs "+(f.origin||"")):
c(h,f.origin)}}var c,d,e=!0;return{kT:function(){return"wpm"},Wba:function(){return!0},init:function(f,h){_.bf.register("rpc",null,function(k){String((k&&k.rpc||{}).disableForceSecure)==="true"&&(e=!1)});c=f;d=h;a("message",b,!1);d("..",!0);return!0},Ib:function(f){d(f,!0);return!0},call:function(f,h,k){var l=_.Xf.eo(f),m=_.Xf.JO(f);l?window.setTimeout(function(){var n=_.Of(k);_.af();m&&"postMessage"in m&&m.postMessage(n,l)},0):f!=".."&&_.$e("No relay set (used as window.postMessage targetOrigin), cannot send cross-domain message");
return!0}}}());if(window.gadgets&&window.gadgets.rpc)typeof _.Xf!="undefined"&&_.Xf||(_.Xf=window.gadgets.rpc,_.Xf.config=_.Xf.config,_.Xf.register=_.Xf.register,_.Xf.unregister=_.Xf.unregister,_.Xf.ZY=_.Xf.registerDefault,_.Xf.i1=_.Xf.unregisterDefault,_.Xf.OS=_.Xf.forceParentVerifiable,_.Xf.call=_.Xf.call,_.Xf.Du=_.Xf.getRelayUrl,_.Xf.Pj=_.Xf.setRelayUrl,_.Xf.KC=_.Xf.setAuthToken,_.Xf.Dw=_.Xf.setupReceiver,_.Xf.Pn=_.Xf.getAuthToken,_.Xf.qK=_.Xf.removeReceiver,_.Xf.KT=_.Xf.getRelayChannel,_.Xf.VY=_.Xf.receive,
_.Xf.WY=_.Xf.receiveSameDomain,_.Xf.getOrigin=_.Xf.getOrigin,_.Xf.eo=_.Xf.getTargetOrigin,_.Xf.JO=_.Xf._getTargetWin,_.Xf.y6=_.Xf._parseSiblingId);else{_.Xf=function(){function a(I,ka){if(!S[I]){var ma=cb;ka||(ma=Oa);S[I]=ma;ka=K[I]||[];for(var Fa=0;Fa<ka.length;++Fa){var T=ka[Fa];T.t=F[I];ma.call(I,T.f,T)}K[I]=[]}}function b(){function I(){Mb=!0}Hb||(typeof window.addEventListener!="undefined"?window.addEventListener("unload",I,!1):typeof window.attachEvent!="undefined"&&window.attachEvent("onunload",
I),Hb=!0)}function c(I,ka,ma,Fa,T){F[ka]&&F[ka]===ma||(_.$e("Invalid gadgets.rpc token. "+F[ka]+" vs "+ma),qb(ka,2));T.onunload=function(){U[ka]&&!Mb&&(qb(ka,1),_.Xf.qK(ka))};b();Fa=_.Nf(decodeURIComponent(Fa))}function d(I,ka){if(I&&typeof I.s==="string"&&typeof I.f==="string"&&I.a instanceof Array)if(F[I.f]&&F[I.f]!==I.t&&(_.$e("Invalid gadgets.rpc token. "+F[I.f]+" vs "+I.t),qb(I.f,2)),I.s==="__ack")window.setTimeout(function(){a(I.f,!0)},0);else{I.c&&(I.callback=function(Ga){_.Xf.call(I.f,(I.g?
"legacy__":"")+"__cb",null,I.c,Ga)});if(ka){var ma=e(ka);I.origin=ka;var Fa=I.r;try{var T=e(Fa)}catch(Ga){}Fa&&T==ma||(Fa=ka);I.referer=Fa}ka=(x[I.s]||x[""]).apply(I,I.a);I.c&&typeof ka!=="undefined"&&_.Xf.call(I.f,"__cb",null,I.c,ka)}}function e(I){if(!I)return"";I=I.split("#")[0].split("?")[0];I=I.toLowerCase();I.indexOf("//")==0&&(I=window.location.protocol+I);I.indexOf("://")==-1&&(I=window.location.protocol+"//"+I);var ka=I.substring(I.indexOf("://")+3),ma=ka.indexOf("/");ma!=-1&&(ka=ka.substring(0,
ma));I=I.substring(0,I.indexOf("://"));if(I!=="http"&&I!=="https"&&I!=="chrome-extension"&&I!=="file"&&I!=="android-app"&&I!=="chrome-search"&&I!=="chrome-untrusted"&&I!=="chrome"&&I!=="devtools")throw Error("l");ma="";var Fa=ka.indexOf(":");if(Fa!=-1){var T=ka.substring(Fa+1);ka=ka.substring(0,Fa);if(I==="http"&&T!=="80"||I==="https"&&T!=="443")ma=":"+T}return I+"://"+ka+ma}function f(I){if(I.charAt(0)=="/"){var ka=I.indexOf("|"),ma=ka>0?I.substring(1,ka):I.substring(1);I=ka>0?I.substring(ka+1):
null;return{id:ma,origin:I}}return null}function h(I){if(typeof I==="undefined"||I==="..")return window.parent;var ka=f(I);if(ka)return k(window.top.frames[ka.id]);I=String(I);return(ka=window.frames[I])?k(ka):(ka=document.getElementById(I))&&ka.contentWindow?ka.contentWindow:null}function k(I){return I?"postMessage"in I?I:I instanceof HTMLIFrameElement&&"contentWindow"in I&&I.contentWindow!=null&&"postMessage"in I.contentWindow?I.contentWindow:null:null}function l(I,ka){if(U[I]!==!0){typeof U[I]===
"undefined"&&(U[I]=0);var ma=h(I);I!==".."&&ma==null||cb.Ib(I,ka)!==!0?U[I]!==!0&&U[I]++<10?window.setTimeout(function(){l(I,ka)},500):(S[I]=Oa,U[I]=!0):U[I]=!0}}function m(I){(I=A[I])&&I.substring(0,1)==="/"&&(I=I.substring(1,2)==="/"?document.location.protocol+I:document.location.protocol+"//"+document.location.host+I);return I}function n(I,ka,ma){ka&&!/http(s)?:\/\/.+/.test(ka)&&(ka.indexOf("//")==0?ka=window.location.protocol+ka:ka.charAt(0)=="/"?ka=window.location.protocol+"//"+window.location.host+
ka:ka.indexOf("://")==-1&&(ka=window.location.protocol+"//"+ka));A[I]=ka;typeof ma!=="undefined"&&(C[I]=!!ma)}function p(I,ka){ka=ka||"";F[I]=String(ka);l(I,ka)}function q(I){I=(I.passReferrer||"").split(":",2);N=I[0]||"none";Y=I[1]||"origin"}function r(I){String(I.useLegacyProtocol)==="true"&&(cb=Wf.AA||Oa,cb.init(d,a))}function w(I,ka){function ma(Fa){Fa=Fa&&Fa.rpc||{};q(Fa);var T=Fa.parentRelayUrl||"";T=e(ba.parent||ka)+T;n("..",T,String(Fa.useLegacyProtocol)==="true");r(Fa);p("..",I)}!ba.parent&&
ka?ma({}):_.bf.register("rpc",null,ma)}function u(I,ka,ma){if(I==="..")w(ma||ba.rpctoken||ba.ifpctok||"",ka);else a:{var Fa=null;if(I.charAt(0)!="/"){if(!_.Ye)break a;Fa=document.getElementById(I);if(!Fa)throw Error("m`"+I);}Fa=Fa&&Fa.src;ka=ka||e(Fa);n(I,ka);ka=_.Ye.Sg(Fa);p(I,ma||ka.rpctoken)}}var x={},A={},C={},F={},O=0,E={},U={},ba={},S={},K={},N=null,Y=null,oa=window.top!==window.self,La=window.name,qb=function(){},fb=window.console,Cb=fb&&fb.log&&function(I){fb.log(I)}||function(){},Oa=function(){function I(ka){return function(){Cb(ka+
": call ignored")}}return{kT:function(){return"noop"},Wba:function(){return!0},init:I("init"),Ib:I("setup"),call:I("call")}}();_.Ye&&(ba=_.Ye.Sg());var Mb=!1,Hb=!1,cb=function(){if(ba.rpctx=="rmr")return Wf.vZ;var I=typeof window.postMessage==="function"?Wf.QN:typeof window.postMessage==="object"?Wf.QN:window.ActiveXObject?Wf.lX?Wf.lX:Wf.AA:navigator.userAgent.indexOf("WebKit")>0?Wf.vZ:navigator.product==="Gecko"?Wf.frameElement:Wf.AA;I||(I=Oa);return I}();x[""]=function(){Cb("Unknown RPC service: "+
this.s)};x.__cb=function(I,ka){var ma=E[I];ma&&(delete E[I],ma.call(this,ka))};return{config:function(I){typeof I.JZ==="function"&&(qb=I.JZ)},register:function(I,ka){if(I==="__cb"||I==="__ack")throw Error("n");if(I==="")throw Error("o");x[I]=ka},unregister:function(I){if(I==="__cb"||I==="__ack")throw Error("p");if(I==="")throw Error("q");delete x[I]},ZY:function(I){x[""]=I},i1:function(){delete x[""]},OS:function(){},call:function(I,ka,ma,Fa){I=I||"..";var T="..";I===".."?T=La:I.charAt(0)=="/"&&(T=
e(window.location.href),T="/"+La+(T?"|"+T:""));++O;ma&&(E[O]=ma);var Ga={s:ka,f:T,c:ma?O:0,a:Array.prototype.slice.call(arguments,3),t:F[I],l:!!C[I]};a:if(N==="bidir"||N==="c2p"&&I===".."||N==="p2c"&&I!==".."){var Ha=window.location.href;var fa="?";if(Y==="query")fa="#";else if(Y==="hash")break a;fa=Ha.lastIndexOf(fa);fa=fa===-1?Ha.length:fa;Ha=Ha.substring(0,fa)}else Ha=null;Ha&&(Ga.r=Ha);if(I===".."||f(I)!=null||document.getElementById(I))(Ha=S[I])||f(I)===null||(Ha=cb),ka.indexOf("legacy__")===
0&&(Ha=cb,Ga.s=ka.substring(8),Ga.c=Ga.c?Ga.c:O),Ga.g=!0,Ga.r=T,Ha?(C[I]&&(Ha=Wf.AA),Ha.call(I,T,Ga)===!1&&(S[I]=Oa,cb.call(I,T,Ga))):K[I]?K[I].push(Ga):K[I]=[Ga]},Du:m,Pj:n,KC:p,Dw:u,Pn:function(I){return F[I]},qK:function(I){delete A[I];delete C[I];delete F[I];delete U[I];delete S[I]},KT:function(){return cb.kT()},VY:function(I,ka){I.length>4?cb.zpa(I,d):c.apply(null,I.concat(ka))},WY:function(I){I.a=Array.prototype.slice.call(I.a);window.setTimeout(function(){d(I)},0)},getOrigin:e,eo:function(I){var ka=
null,ma=m(I);ma?ka=ma:(ma=f(I))?ka=ma.origin:I==".."?ka=ba.parent:(I=document.getElementById(I))&&I.tagName.toLowerCase()==="iframe"&&(ka=I.src);return e(ka)},init:function(){cb.init(d,a)===!1&&(cb=Oa);oa?u(".."):_.bf.register("rpc",null,function(I){I=I.rpc||{};q(I);r(I)})},JO:h,y6:f,Gha:"__ack",Gma:La||"..",Qma:0,Pma:1,Oma:2}}();_.Xf.init()};_.Xf.config({JZ:function(a){throw Error("r`"+a);}});_.t("gadgets.rpc.config",_.Xf.config);_.t("gadgets.rpc.register",_.Xf.register);_.t("gadgets.rpc.unregister",_.Xf.unregister);_.t("gadgets.rpc.registerDefault",_.Xf.ZY);_.t("gadgets.rpc.unregisterDefault",_.Xf.i1);_.t("gadgets.rpc.forceParentVerifiable",_.Xf.OS);_.t("gadgets.rpc.call",_.Xf.call);_.t("gadgets.rpc.getRelayUrl",_.Xf.Du);_.t("gadgets.rpc.setRelayUrl",_.Xf.Pj);_.t("gadgets.rpc.setAuthToken",_.Xf.KC);_.t("gadgets.rpc.setupReceiver",_.Xf.Dw);_.t("gadgets.rpc.getAuthToken",_.Xf.Pn);
_.t("gadgets.rpc.removeReceiver",_.Xf.qK);_.t("gadgets.rpc.getRelayChannel",_.Xf.KT);_.t("gadgets.rpc.receive",_.Xf.VY);_.t("gadgets.rpc.receiveSameDomain",_.Xf.WY);_.t("gadgets.rpc.getOrigin",_.Xf.getOrigin);_.t("gadgets.rpc.getTargetOrigin",_.Xf.eo);
var Ug={Rha:"Authorization",A2:"Content-ID",qia:"Content-Transfer-Encoding",ria:"Content-Type",Yia:"Date",Pla:"OriginToken",mka:"hotrod-board-name",nka:"hotrod-chrome-cpu-model",oka:"hotrod-chrome-processors",Aoa:"WWW-Authenticate",Coa:"X-Ad-Manager-Impersonation",Boa:"X-Ad-Manager-Debug-Info",Eoa:"X-ClientDetails",Foa:"X-Cloudaicompanion-Trace-Id",Goa:"X-Compass-Routing-Destination",Joa:"X-Goog-AuthUser",Ooa:"X-Goog-Encode-Response-If-Executable",Hoa:"X-Google-Consent",Ioa:"X-Google-EOM",Qoa:"X-Goog-Meeting-ABR",
Roa:"X-Goog-Meeting-Botguardid",Soa:"X-Goog-Meeting-Bot-Info",Toa:"X-Goog-Meeting-ClientInfo",Uoa:"X-Goog-Meeting-ClientVersion",Voa:"X-Goog-Meeting-Debugid",Woa:"X-Goog-Meeting-Identifier",Xoa:"X-Goog-Meeting-Interop-Cohorts",Yoa:"X-Goog-Meeting-Interop-Type",Zoa:"X-Goog-Meeting-OidcIdToken",apa:"X-Goog-Meeting-RtcClient",bpa:"X-Goog-Meeting-StartSource",cpa:"X-Goog-Meeting-Token",dpa:"X-Goog-Meeting-Viewer-Token",epa:"X-Goog-PageId",gpa:"X-Goog-Safety-Content-Type",hpa:"X-Goog-Safety-Encoding",
Loa:"X-Goog-Drive-Client-Version",Moa:"X-Goog-Drive-Resource-Keys",ipa:"X-HTTP-Method-Override",jpa:"X-JavaScript-User-Agent",kpa:"X-Origin",lpa:"X-Referer",mpa:"X-Requested-With",ppa:"X-Use-HTTP-Status-Code-Override",npa:"X-Server-Timeout",Poa:"X-Goog-First-Party-Reauth",opa:"X-Server-Token",Koa:"x-goog-chat-space-id",fpa:"x-goog-pan-request-context",Doa:"X-AppInt-Credentials",Noa:"X-Goog-Earth-Gcp-Project"},Vg="Accept Accept-Language Authorization Cache-Control cast-device-capabilities Content-Disposition Content-Encoding Content-Language Content-Length Content-MD5 Content-Range Content-Transfer-Encoding Content-Type Date developer-token EES-S7E-MODE financial-institution-id GData-Version google-cloud-resource-prefix hotrod-board-name hotrod-chrome-cpu-model hotrod-chrome-processors Host If-Match If-Modified-Since If-None-Match If-Unmodified-Since linked-customer-id login-customer-id MIME-Version Origin OriginToken Pragma Range request-id Slug Transfer-Encoding Want-Digest X-Ad-Manager-Impersonation X-Ad-Manager-Debug-Info x-alkali-account-key x-alkali-application-key x-alkali-auth-apps-namespace x-alkali-auth-entities-namespace x-alkali-auth-entity x-alkali-client-locale x-chrome-connected x-framework-xsrf-token X-Client-Data X-Client-Pctx X-ClientDetails X-Client-Version x-debug-settings-metadata X-Firebase-Locale X-GData-Client X-GData-Key X-Goog-AuthUser X-Goog-PageId X-Goog-Encode-Response-If-Executable X-GoogApps-Allowed-Domains X-Goog-AdX-Buyer-Impersonation X-Goog-Api-Client X-Goog-Api-Key X-Google-EOM X-Goog-Visibilities X-Goog-Correlation-Id X-Goog-Request-Info X-Goog-Request-Reason X-Goog-Request-Time X-Goog-Experiments x-goog-ext-*********-jspb x-goog-ext-*********-jspb x-goog-ext-*********-jspb x-goog-ext-*********-jspb x-goog-ext-*********-jspb x-goog-ext-*********-jspb x-goog-ext-*********-jspb x-goog-ext-*********-bin x-goog-ext-*********-bin x-goog-ext-*********-jspb x-goog-ext-*********-bin x-goog-ext-*********-bin x-goog-ext-*********-bin x-goog-ext-*********-bin x-goog-ext-*********-bin x-goog-ext-*********-bin x-goog-ext-*********-jspb x-goog-ext-*********-bin X-Goog-Firebase-Installations-Auth x-goog-greenenergyuserappservice-metadata X-Firebase-Client X-Firebase-Client-Log-Type X-Firebase-GMPID X-Firebase-Auth-Token X-Firebase-AppCheck X-Firebase-Token X-Goog-Drive-Client-Version X-Goog-Drive-Resource-Keys x-goog-iam-authority-selector x-goog-iam-authorization-token x-goog-request-params x-goog-sherlog-context X-Goog-Sn-Metadata X-Goog-Sn-PatientId X-Goog-Spatula X-Goog-Travel-Bgr X-Goog-Travel-Settings X-Goog-Upload-Command X-Goog-Upload-Content-Disposition X-Goog-Upload-Content-Length X-Goog-Upload-Content-Type X-Goog-Upload-File-Name X-Goog-Upload-Header-Content-Encoding X-Goog-Upload-Header-Content-Length X-Goog-Upload-Header-Content-Type X-Goog-Upload-Header-Transfer-Encoding X-Goog-Upload-Offset X-Goog-Upload-Protocol X-Goog-User-Project X-Goog-Visitor-Id X-Goog-FieldMask X-Google-Project-Override x-goog-maps-api-salt x-goog-maps-api-signature x-goog-maps-client-id x-goog-maps-channel-id x-goog-maps-solution-id x-goog-maps-session-id x-goog-maps-traffic-policy x-goog-gmp-client-signals x-goog-spanner-database-role X-HTTP-Method-Override X-JavaScript-User-Agent X-Pan-Versionid X-Proxied-User-IP X-Origin X-Referer X-Requested-With X-Stadia-Client-Context X-Upload-Content-Length X-Upload-Content-Type X-Use-Alt-Service X-Use-HTTP-Status-Code-Override X-Ios-Bundle-Identifier X-Places-Ios-Sdk X-Android-Package X-Android-Cert X-Places-Android-Sdk X-Goog-Maps-Ios-Uuid X-Goog-Maps-Android-Uuid X-Ariane-Xsrf-Token X-Earth-Engine-App-ID-Token X-Earth-Engine-Computation-Profile X-Earth-Engine-Computation-Profiling X-Play-Console-Experiments-Override X-Play-Console-Session-Id X-YouTube-Bootstrap-Logged-In X-Youtube-Client-Version X-Youtube-Lava-Device-Context X-YouTube-VVT X-YouTube-Page-CL X-YouTube-Page-Label X-YouTube-Page-Timestamp X-Compass-Routing-Destination X-Goog-Meeting-ABR X-Goog-Meeting-Botguardid X-Goog-Meeting-Bot-Info X-Goog-Meeting-ClientInfo X-Goog-Meeting-ClientVersion X-Goog-Meeting-Debugid X-Goog-Meeting-Identifier X-Goog-Meeting-Interop-Cohorts X-Goog-Meeting-Interop-Type X-Goog-Meeting-OidcIdToken X-Goog-Meeting-RtcClient X-Goog-Meeting-StartSource X-Goog-Meeting-Token X-Goog-Meeting-Viewer-Token x-sdm-id-token X-Sfdc-Authorization X-Server-Timeout x-foyer-client-environment X-Goog-First-Party-Reauth X-Server-Token x-rfui-request-context x-goog-chat-space-id x-goog-nest-jwt X-Cloud-Trace-Context traceparent x-goog-pan-request-context X-AppInt-Credentials X-Goog-Earth-Gcp-Project".split(" "),
Wg="Digest Cache-Control Content-Disposition Content-Encoding Content-Language Content-Length Content-MD5 Content-Range Content-Transfer-Encoding Content-Type Date ETag Expires Last-Modified Location Pragma Range Server Transfer-Encoding WWW-Authenticate Vary Unzipped-Content-MD5 X-Correlation-ID X-Debug-Tracking-Id X-Google-Consent X-Google-EOM X-Goog-Generation X-Goog-Metageneration X-Goog-Safety-Content-Type X-Goog-Safety-Encoding X-Google-Trace X-Goog-Upload-Chunk-Granularity X-Goog-Upload-Control-URL X-Goog-Upload-Size-Received X-Goog-Upload-Status X-Goog-Upload-URL X-Goog-Diff-Download-Range X-Goog-Hash X-Goog-Updated-Authorization X-Server-Object-Version X-Guploader-Customer X-Guploader-Upload-Result X-Guploader-Uploadid X-Google-Gfe-Backend-Request-Cost X-Earth-Engine-Computation-Profile X-Cloudaicompanion-Trace-Id X-Goog-Meeting-ABR X-Goog-Meeting-Botguardid X-Goog-Meeting-Bot-Info X-Goog-Meeting-ClientInfo X-Goog-Meeting-ClientVersion X-Goog-Meeting-Debugid X-Goog-Meeting-Identifier X-Goog-Meeting-RtcClient X-Goog-Meeting-Token X-Goog-Meeting-Viewer-Token X-Compass-Routing-Destination".split(" ");var Xg,Yg,Zg,$g,bh,ch,dh,eh,fh,gh,hh,ih;Xg=null;Yg=null;Zg=null;$g=function(a,b){var c=a.length;if(c!=b.length)return!1;for(var d=0;d<c;++d){var e=a.charCodeAt(d),f=b.charCodeAt(d);e>=65&&e<=90&&(e+=32);f>=65&&f<=90&&(f+=32);if(e!=f)return!1}return!0};
_.ah=function(a){a=String(a||"").split("\x00").join("");for(var b=[],c=!0,d=a.length,e=0;e<d;++e){var f=a.charAt(e),h=a.charCodeAt(e);if(h>=55296&&h<=56319&&e+1<d){var k=a.charAt(e+1),l=a.charCodeAt(e+1);l>=56320&&l<=57343&&(f+=k,h=65536+(h-55296<<10)+(l-56320),++e)}if(!(h>=0&&h<=1114109)||h>=55296&&h<=57343||h>=64976&&h<=65007||(h&65534)==65534)h=65533,f=String.fromCharCode(h);k=!(h>=32&&h<=126)||f==" "||c&&f==":"||f=="\\";!c||f!="/"&&f!="?"||(c=!1);f=="%"&&(e+2>=d?k=!0:(l=16*parseInt(a.charAt(e+
1),16)+parseInt(a.charAt(e+2),16),l>=0&&l<=255?(h=l,f=h==0?"":"%"+(256+l).toString(16).toUpperCase().substr(1),e+=2):k=!0));k&&(f=encodeURIComponent(f),f.length<=1&&(h>=0&&h<=127?f="%"+(256+h).toString(16).toUpperCase().substr(1):(h=65533,f=encodeURIComponent(String.fromCharCode(h)))));b.push(f)}a=b.join("");a=a.split("#")[0];a=a.split("?");b=a[0].split("/");c=[];d=b.length;for(e=0;e<d;++e)f=b[e],h=f.split("%2E").join("."),h=h.split(encodeURIComponent("\uff0e")).join("."),h=="."?e+1==d&&c.push(""):
h==".."?(c.length>0&&c.pop(),e+1==d&&c.push("")):c.push(f);a[0]=c.join("/");for(a=a.join("?");a&&a.charAt(0)=="/";)a=a.substr(1);return"/"+a};bh={"access-control-allow-origin":!0,"access-control-allow-credentials":!0,"access-control-expose-headers":!0,"access-control-max-age":!0,"access-control-allow-headers":!0,"access-control-allow-methods":!0,p3p:!0,"proxy-authenticate":!0,"set-cookie":!0,"set-cookie2":!0,status:!0,tsv:!0,"":!0};
ch={"accept-charset":!0,"accept-encoding":!0,"access-control-request-headers":!0,"access-control-request-method":!0,"client-ip":!0,clientip:!0,connection:!0,"content-length":!0,cookie:!0,cookie2:!0,date:!0,dnt:!0,expect:!0,forwarded:!0,"forwarded-for":!0,"front-end-https":!0,host:!0,"keep-alive":!0,"max-forwards":!0,method:!0,origin:!0,"raw-post-data":!0,referer:!0,te:!0,trailer:!0,"transfer-encoding":!0,upgrade:!0,url:!0,"user-agent":!0,version:!0,via:!0,"x-att-deviceid":!0,"x-chrome-connected":!0,
"x-client-data":!0,"x-client-ip":!0,"x-do-not-track":!0,"x-forwarded-by":!0,"x-forwarded-for":!0,"x-forwarded-host":!0,"x-forwarded-proto":!0,"x-geo":!0,"x-googapps-allowed-domains":!0,"x-origin":!0,"x-proxyuser-ip":!0,"x-real-ip":!0,"x-referer":!0,"x-uidh":!0,"x-user-ip":!0,"x-wap-profile":!0,"":!0};
dh=function(a){if(!_.hd(a))return null;for(var b={},c=0;c<a.length;c++){var d=a[c];if(typeof d==="string"&&d){var e=d.toLowerCase();$g(d,e)&&(b[e]=d)}}for(var f in Ug)Object.prototype.hasOwnProperty.call(Ug,f)&&(a=Ug[f],c=a.toLowerCase(),$g(a,c)&&Object.prototype.hasOwnProperty.call(b,c)&&(b[c]=a));return b};eh=new RegExp("("+/[\t -~\u00A0-\u2027\u202A-\uD7FF\uE000-\uFFFF]/.source+"|"+/[\uD800-\uDBFF][\uDC00-\uDFFF]/.source+"){1,100}","g");fh=/[ \t]*(\r?\n[ \t]+)+/g;gh=/^[ \t]+|[ \t]+$/g;
hh=function(a,b){if(!b&&typeof a==="object"&&a&&typeof a.length==="number"){b=a;a="";for(var c=b.length,d=0;d<c;++d){var e=hh(b[d],!0);e&&(a&&(e=a+", "+e),a=e)}}if(typeof a==="string"&&(a=a.replace(fh," "),a=a.replace(gh,""),a.replace(eh,"")==""&&a))return a};ih=/^[-0-9A-Za-z!#\$%&'\*\+\.\^_`\|~]+$/g;
_.jh=function(a){if(typeof a!=="string"||!a||!a.match(ih))return null;a=a.toLowerCase();if(Zg==null){var b=[],c=_.Ue("googleapis/headers/response");c&&typeof c==="object"&&typeof c.length==="number"||(c=null);c!=null&&(b=b.concat(c));(c=_.Ue("client/headers/response"))&&typeof c==="object"&&typeof c.length==="number"||(c=null);c!=null&&(b=b.concat(c));b=b.concat(Wg);(c=_.Ue("googleapis/headers/request"))&&typeof c==="object"&&typeof c.length==="number"||(c=null);c!=null&&(b=b.concat(c));(c=_.Ue("client/headers/request"))&&
typeof c==="object"&&typeof c.length==="number"||(c=null);c!=null&&(b=b.concat(c));b=b.concat(Vg);for(var d in Ug)Object.prototype.hasOwnProperty.call(Ug,d)&&b.push(Ug[d]);Zg=dh(b)}return Zg!=null&&Zg.hasOwnProperty(a)?Zg[a]:a};
_.kh=function(a,b){if(!_.jh(a)||!hh(b))return null;a=a.toLowerCase();if(a.match(/^x-google|^x-gfe|^proxy-|^sec-/i)||ch[a])return null;if(Xg==null){b=[];var c=_.Ue("googleapis/headers/request");c&&typeof c==="object"&&typeof c.length==="number"||(c=null);c!=null&&(b=b.concat(c));(c=_.Ue("client/headers/request"))&&typeof c==="object"&&typeof c.length==="number"||(c=null);c!=null&&(b=b.concat(c));b=b.concat(Vg);Xg=dh(b)}return Xg!=null&&Xg.hasOwnProperty(a)?Xg[a]:null};
_.lh=function(a,b){if(!_.jh(a)||!hh(b))return null;a=a.toLowerCase();if(bh[a])return null;if(Yg==null){b=[];var c=_.Ue("googleapis/headers/response");c&&typeof c==="object"&&typeof c.length==="number"||(c=null);c!=null&&(b=b.concat(c));(c=_.Ue("client/headers/response"))&&typeof c==="object"&&typeof c.length==="number"||(c=null);c!=null&&(b=b.concat(c));b=b.concat(Wg);Yg=dh(b)}return Yg!=null&&Yg.hasOwnProperty(a)?a:null};
_.mh=function(a,b){if(_.jh(b)&&a!=null&&typeof a==="object"){var c=void 0,d;for(d in a)if(Object.prototype.hasOwnProperty.call(a,d)&&$g(d,b)){var e=hh(a[d]);e&&(c!==void 0&&(e=c+", "+e),c=e)}return c}};_.nh=function(a,b,c,d){var e=_.jh(b);if(e){c&&(c=hh(c));b=b.toLowerCase();for(var f in a)Object.prototype.hasOwnProperty.call(a,f)&&$g(f,b)&&delete a[f];c&&(d||(b=e),a[b]=c)}};
_.oh=function(a,b){var c={};if(!a)return c;a=a.split("\r\n");for(var d=a.length,e=0;e<d;++e){var f=a[e];if(!f)break;var h=f.indexOf(":");if(!(h<=0)){var k=f.substring(0,h);if(k=_.jh(k)){for(f=f.substring(h+1);e+1<d&&a[e+1].match(/^[ \t]/);)f+="\r\n"+a[e+1],++e;if(f=hh(f))if(k=_.lh(k,f)||(b?void 0:k))k=k.toLowerCase(),h=_.mh(c,k),h!==void 0&&(f=h+", "+f),_.nh(c,k,f,!0)}}}return c};
var qh;_.ph=function(a){_.Xa.setTimeout(function(){throw a;},0)};qh=0;_.rh=function(a){return Object.prototype.hasOwnProperty.call(a,_.ab)&&a[_.ab]||(a[_.ab]=++qh)};
_.sh=function(){return _.Kc("Firefox")||_.Kc("FxiOS")};_.th=function(){return _.Kc("Safari")&&!(_.Tc()||(_.Mc()?0:_.Kc("Coast"))||_.Nc()||_.Pc()||_.Qc()||_.Sc()||_.sh()||_.Kc("Silk")||_.Kc("Android"))};_.uh=function(){return _.Kc("Android")&&!(_.Tc()||_.sh()||_.Nc()||_.Kc("Silk"))};_.vh=_.sh();_.wh=_.Yc()||_.Kc("iPod");_.xh=_.Kc("iPad");_.zh=_.uh();_.Ah=_.Tc();_.Bh=_.th()&&!_.Zc();
_.Ch=function(a,b){for(var c=1;c<arguments.length;c++){var d=arguments[c];if(_.hd(d)){var e=a.length||0,f=d.length||0;a.length=e+f;for(var h=0;h<f;h++)a[e+h]=d[h]}else a.push(d)}};_.Dh=function(a,b){b=b||a;for(var c=0,d=0,e={};d<a.length;){var f=a[d++],h=_.vb(f)?"o"+_.rh(f):(typeof f).charAt(0)+f;Object.prototype.hasOwnProperty.call(e,h)||(e[h]=!0,b[c++]=f)}b.length=c};_.Eh=function(a){for(var b in a)return!1;return!0};
_.Fh=function(a,b){a.src=_.ic(b);(b=_.Ec("script",a.ownerDocument))&&a.setAttribute("nonce",b)};_.Gh=function(a){for(var b=[],c=0,d=0;d<a.length;d++){var e=a.charCodeAt(d);e>255&&(b[c++]=e&255,e>>=8);b[c++]=e}return b};var Hh,Ih,Kh;Hh={};Ih=null;_.Jh=_.yd||_.zd||!_.Bh&&typeof _.Xa.atob=="function";_.Lh=function(a,b){b===void 0&&(b=0);Kh();b=Hh[b];for(var c=Array(Math.floor(a.length/3)),d=b[64]||"",e=0,f=0;e<a.length-2;e+=3){var h=a[e],k=a[e+1],l=a[e+2],m=b[h>>2];h=b[(h&3)<<4|k>>4];k=b[(k&15)<<2|l>>6];l=b[l&63];c[f++]=m+h+k+l}m=0;l=d;switch(a.length-e){case 2:m=a[e+1],l=b[(m&15)<<2]||d;case 1:a=a[e],c[f]=b[a>>2]+b[(a&3)<<4|m>>4]+l+d}return c.join("")};
_.Mh=function(a,b){function c(l){for(;d<a.length;){var m=a.charAt(d++),n=Ih[m];if(n!=null)return n;if(!_.wc(m))throw Error("w`"+m);}return l}Kh();for(var d=0;;){var e=c(-1),f=c(0),h=c(64),k=c(64);if(k===64&&e===-1)break;b(e<<2|f>>4);h!=64&&(b(f<<4&240|h>>2),k!=64&&b(h<<6&192|k))}};
Kh=function(){if(!Ih){Ih={};for(var a="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split(""),b=["+/=","+/","-_=","-_.","-_"],c=0;c<5;c++){var d=a.concat(b[c].split(""));Hh[c]=d;for(var e=0;e<d.length;e++){var f=d[e];Ih[f]===void 0&&(Ih[f]=e)}}}};
_.Nh=function(a){var b=[],c=0,d;for(d in a)b[c++]=d;return b};_.Oh=function(a){return a==null?"":String(a)};_.Ph=function(a,b,c,d,e,f,h){var k="";a&&(k+=a+":");c&&(k+="//",b&&(k+=b+"@"),k+=c,d&&(k+=":"+d));e&&(k+=e);f&&(k+="?"+f);h&&(k+="#"+h);return k};_.Qh=RegExp("^(?:([^:/?#.]+):)?(?://(?:([^\\\\/?#]*)@)?([^\\\\/?#]*?)(?::([0-9]+))?(?=[\\\\/?#]|$))?([^?#]+)?(?:\\?([^#]*))?(?:#([\\s\\S]*))?$");
_.Rh=function(a,b){if(!b)return a;var c=a.indexOf("#");c<0&&(c=a.length);var d=a.indexOf("?");if(d<0||d>c){d=c;var e=""}else e=a.substring(d+1,c);a=[a.slice(0,d),e,a.slice(c)];c=a[1];a[1]=b?c?c+"&"+b:b:c;return a[0]+(a[1]?"?"+a[1]:"")+a[2]};_.Sh=function(a,b,c){if(Array.isArray(b))for(var d=0;d<b.length;d++)_.Sh(a,String(b[d]),c);else b!=null&&c.push(a+(b===""?"":"="+encodeURIComponent(String(b))))};_.Th=function(a){var b=[],c;for(c in a)_.Sh(c,a[c],b);return b.join("&")};
_.Uh=function(a,b){b=_.Th(b);return _.Rh(a,b)};
var Vh,Wh=function(){try{return new XMLHttpRequest}catch(a){}try{return new ActiveXObject("Msxml2.XMLHTTP")}catch(a){}return null},Xh=function(a){var b=_.ah(a);if(String(a)!=b)throw Error("x");(a=b)&&a.charAt(a.length-1)=="/"||(a=(a||"")+"/");_.Xf.register("init",function(){Xh(a)});Vh=a;_.Ye.Sg(window.location.href)},Yh=function(a,b,c,d){var e={};if(b)for(var f in b)if(Object.prototype.hasOwnProperty.call(b,f)){var h=_.mh(b,f),k=_.lh(f,h);k&&h!==void 0&&_.nh(e,k,h,!0)}return{body:a,headers:e,status:typeof c===
"number"?c:void 0,statusText:d||void 0}},Zh=function(a,b){a={error:{code:-1,message:a}};if(b.url=="/rpc"){b=b.body;for(var c=[],d=0;d<b.length;d++){var e=_.Of(a);e=_.Nf(e);e.id=b[d].id;c.push(e)}a=c}return _.Of(a)},$h=function(a,b,c,d){a=a||{};var e=a.headers||{},f=a.httpMethod||"GET",h=String(a.url||""),k=a.urlParams||null,l=a.body||null;c=c||null;d=d||null;h=_.ah(h);h=Vh+String(h||"/").substr(1);h=_.Uh(h,k);var m=[];k=[];for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){m.push(n);var p=
_.mh(e,n);p!==void 0&&(n=_.kh(n,p))&&k.push([n,p])}for(;m.length;)delete e[m.pop()];for(;k.length;)n=k.pop(),_.nh(e,n[0],n[1]);_.nh(e,"X-Origin",c||void 0);_.nh(e,"X-Referer",d||void 0);_.nh(e,"X-Goog-Encode-Response-If-Executable","base64");l&&typeof l==="object"&&(l=_.Of(l));var q=Wh();if(!q)throw Error("y");q.open(f,h);q.onreadystatechange=function(){if(q.readyState==4&&q.status!==0){var w=q.responseText;var u=q.getAllResponseHeaders();u=_.oh(u,!0);w=Yh(w,u,q.status,q.statusText);b(w)}};q.onerror=
function(){var w=Zh("A network error occurred, and the request could not be completed.",a);w=Yh(w);b(w)};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(f=e[r],q.setRequestHeader(unescape(encodeURIComponent(r)),unescape(encodeURIComponent(f))));q.send(l?l:null)},ai=function(a,b,c,d){var e={},f=0;if(a.length==0)b(e);else{var h=function(k){var l=k.key;k=k.params;try{$h(k,function(n){e[l]={data:n};f++;a.length==f?b(_.Of(e)):h(a[f])},c,d)}catch(n){var m="";n&&(m+=" [",n.name&&(m+=n.name+": "),
m+=n.message||String(n),m+="]");k=Zh("An error occurred, and the request could not be completed."+m,k);k=Yh(k);e[l]={data:k};f++;a.length==f?b(_.Of(e)):h(a[f])}};h(a[f])}};_.Gg=_.Gg||{};_.Gg.rea=function(){_.Xf.register("makeHttpRequests",function(a){this.f==".."&&this.t==_.Xf.Pn("..")&&this.origin==_.Xf.eo("..")&&ai.call(this,a,this.callback,this.origin,this.referer)})};
_.Gg.init=function(){var a=String(window.location.pathname);a.length>=18&&a.substr(a.length-18)=="/static/proxy.html"&&(a=a.substr(0,a.length-18));a||(a="/");_.Gg.oV(a)};_.Gg.oV=function(a){var b=_.ah(a);if(String(a)!=b)throw Error("x");_.Gg.rea();Xh(a);_.Xf.call("..","ready:"+_.Xf.Pn(".."))};_.t("googleapis.ApiServer.makeHttpRequests",ai);_.t("googleapis.ApiServer.initWithPath",Xh);_.t("googleapis.server.init",_.Gg.init);_.t("googleapis.server.initWithPath",_.Gg.oV);
});
// Google Inc.
