import pygame
import random
import time

"""
Citations:

- https://stackoverflow.com/questions/25668828/how-to-create-colour-gradient-in-python (not really used)
- https://stackoverflow.com/questions/14224518/what-is-a-good-way-to-get-keyboard-input-in-python-pygame
- https://stackoverflow.com/questions/12879225/pygame-applying-transparency-to-an-image-with-alpha 
- https://youtu.be/8_HVdxBqJmE?si=VnXQlCUX2SAd-wmh 
"""

"""
Notes:

- event.unicode:  this captures the actual character typed, not just the key code
- set_alpha(): found this method to create blur effects 
- Gradient blending: learned how to create smooth transitions between colors using blend factors
"""

"""
Features:
    - using L,R arrow keys to change colors(R= next, L= previous)
    - Using G to turn ON/Off gradient
    - Once G is turned ON, the gradient direction can be changed by using the four arrow keys(<, >, ^, v) ---> so hard + no tutorials
                ---> changed idea: grdaient and G is still there, but X/Y keys are used to change gradient direction.
                        ---> changed idea: grdaient is f1, and to change gradient direction use f2.



"""

# =================== How I Applied Them/ what did I think of =======================

"""
First Feature:  WORKING ✅
 Solid color changing

--> TO  LOOK FOR THIS CODE, (ctrl f) 👻

So what am I doing here?
-the trail is green rn. And I want to chnage colors using arrow keys.
1. we need to pass "current_color" for parameter color when creating the trail. (DONE)
2. add arrow key logic. (DONE)
  if right arrow key is pressed:
        current color will increase by 1.
  elif left arrow key is pressed:
        current color will decrease by 1

3. an issue occured. the colors do not loop, when the index reaches 0 it doesnt subtract, and when its 7 it doesnt add.
(OUT OF RANGE)

How to fix?
 - well, (0-1)%8 = 7
  and (7+1)%8 = 0

  USE %
"""

"""
Second Feature: WORKING ✅
    Gradient

--> To look for some of this code, (ctrl f) 🦄 and to file "ColorWave.py"*(I tried to make seperate files but it was broken😥)
So what am I doing here?
 - it is really hard to add the gradient, no tutorials at all!
 I thought of an idea:

    1. divide the trail into sections.
    2. each section have their own color.
    3. after a specific time, each color of each section will change.
    4. add G key logic
    4. the colors loop.
    5. the sections can be divided vertically (Y),
    or horizontally (x), and that is how we make an illusion
    of changing direction.(add Y, X keys logic)
    6. make the colors mix.
    (EX: section 1 is blue, and section 2 is red, the tips where they meet will mix
    (or the trail shared between them))

I realized something:
     - Iam planning to add user input. As a person types, their words fall off the screen.
        But I am using (G, X, Y) keys.
     - I need to choose new keys for the 2nd feature

How abt:
- F1 for G
- F2 for X/Y Mode
"""

"""
Third Feature: WORKING ✅
    More trails with different depths(Just making it look more like a matrix)

--> To look for some of this code, (ctrl f) 🐠

So what am I doing here?
- I want to make the matrix effect better(more like a matrix ig) with depth and stuff like that
1. create multiple layers of trails with different speeds and lengths
2. make trails further away appear dimmer
3. add varying speeds based on proximity
4. random trails with different lengths and speeds
5. blur effect for background trails
6. flickering effect
"""
"""
Fourth Features: WORKING ✅
    making clicked keys on the keyboard fall off the screen with random color

--> To look for some of this code, (ctrl f) 💅

So what am I doing here?
- I want to make typed keys appear as falling characters

1. detect keyboard input and create a new trail for each pressed key
2. special colors for typed characters different from COLORS
3. make typed characters larger
4. yped characters disappear after 3 seconds
5. MAKE SURE typed characters have different colors than the current background
"""


#matrix characters
characters = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789$-=+[]{}:;,./<>()~!@#%^&*_\\|?日本語中文"  


COLORS = [
    (0, 180, 0),      # 0, bright green
    (0, 220, 100),    # 1, neon green
    (0, 150, 150),    # 2, teal
    (100, 0, 180),    # 3, purple
    (0, 100, 200),    # 4, bright blue
    (180, 0, 100),    # 5, magenta
    (200, 100, 0),    # 6, amber
    (150, 150, 150)   # 7, silver
]

#🦄gradient stuff
NUM_SECTIONS = 10 #number of sections
gradient_active = False
gradient_speed = 0.15 #moderate speed 
section_mode = "y"
SCREEN_WIDTH = 1200
SCREEN_HEIGHT = 800

class ColorWave:
    def __init__(self):
        self.section_mode = "y"
        self.last_update = time.time()
        self.create_sections()
        self.current_time = 0


    def create_sections(self):

        self.sections = []

        if self.section_mode == "x":

            section_size = SCREEN_WIDTH//NUM_SECTIONS

            for i in range(NUM_SECTIONS):

                self.sections.append({
                    'x_start': i * section_size,
                    'x_end': (i+1) * section_size,
                    'color_offset': i*(len(COLORS)/NUM_SECTIONS),
                })

        else:
            section_size = SCREEN_HEIGHT// NUM_SECTIONS

            for i in range(NUM_SECTIONS):

                self.sections.append({
                    'y_start': i * section_size,
                    'y_end': (i + 1) * section_size,
                    'color_offset': i * (len(COLORS) / NUM_SECTIONS)
                })

    def get_section_color(self, x, y, current_time):

        if self.section_mode == 'x':

            for i, section in enumerate(self.sections):

                if section['x_start'] <= x < section['x_end']:

                    color_pos = (current_time + section['color_offset']) % len(COLORS)
                    base_color = self.get_blended_color(color_pos)

                    blend_area = 50  #smoother transitions

                    if x > section['x_end'] - blend_area and i < len(self.sections)-1:

                        next_color_pos = (current_time + self.sections[i+1]['color_offset']) % len(COLORS)
                        next_color = self.get_blended_color(next_color_pos)
                        blend_factor = (x - (section['x_end'] - blend_area)) / blend_area
                        return (
                            int(base_color[0]*(1-blend_factor) + next_color[0]*blend_factor),
                            int(base_color[1]*(1-blend_factor) + next_color[1]*blend_factor),
                            int(base_color[2]*(1-blend_factor) + next_color[2]*blend_factor)
                        )
                    return base_color

        else:

            for section in self.sections:

                if section['y_start'] <= y < section['y_end']:

                    color_pos = (current_time + section['color_offset']) % len(COLORS)
                    return self.get_blended_color(color_pos)

        return (0, 0, 0)  #default color

    def get_blended_color(self, color_pos):

        idx1 = int(color_pos)
        idx2 = (idx1 + 1) % len(COLORS)
        blend = color_pos % 1

        #transition stays longer at each color
        blend_factor = blend * blend * (3 - 2 * blend)

        #to ensure values stay within 0-255 range
        r = max(0, min(255, int(COLORS[idx1][0]*(1-blend_factor) + COLORS[idx2][0]*blend_factor)))
        g = max(0, min(255, int(COLORS[idx1][1]*(1-blend_factor) + COLORS[idx2][1]*blend_factor)))
        b = max(0, min(255, int(COLORS[idx1][2]*(1-blend_factor) + COLORS[idx2][2]*blend_factor)))

        return (r, g, b)

    def update(self):
        self.current_time = time.time() * gradient_speed


current_color = COLORS[2]

class Trail:
    def __init__(self,  textsize, speed, position, length, font):

        self.length = length
        self.speed = speed
        self.trail = []
        self.is_typed = False  #flag to identify typed characters
        self.creation_time = time.time()  #when this trail was created

        for i in range(length):

            random_symbol = random.choice(characters)
            y_pos = position[1]-(textsize*i*2.5)  #more vertical space between characters

            #🐠depth
            if i <= 2:
                depth_factor = 1.0  #full brightness

            else:
                depth_factor = 0.8  #next characters dimmer

            

            brightness_boost = 1.15  

            r = int(min(255, current_color[0] * depth_factor * brightness_boost))
            g = int(min(255, current_color[1] * depth_factor * brightness_boost))
            b = int(min(255, current_color[2] * depth_factor * brightness_boost))
            char_color = (r, g, b)  #brighter colors

            #new font
            symbol_font = pygame.font.SysFont("Consolas", textsize)

            #alpha calculation for a blur 
            if i <= 2:
                alpha = 255  
            else:
                alpha = 230 
           
            properties = {
                "font": symbol_font,
                "color": char_color,
                "symbol": random_symbol,
                "textsize": textsize,
                "position": (position[0],y_pos),
                "text": symbol_font.render(random_symbol,True,char_color),
                "should_change": random.random() < 1.0, #💅60% chance to change 
                "last_change": time.time(),
                "change_time": random.uniform(0.2, 1.0),
                "depth_factor": depth_factor,
                "alpha": alpha
            }

            # Only set alpha if needed
            if alpha < 255:
                properties["text"].set_alpha(alpha)
            self.trail.append(properties)



pygame.init()  # INITIALIZE PYGAME HERE

#SETUP WINDOW
SCREEN_WIDTH = 1200
SCREEN_HEIGHT = 800
screen = pygame.display.set_mode([SCREEN_WIDTH, SCREEN_HEIGHT])
pygame.display.set_caption("Matrix Effect")
clock = pygame.time.Clock()


font_size = 24
font = pygame.font.SysFont("Consolas", font_size)  
trails = []         #trails
trail_length = 15
trail_speed = 3


#🐠 evenly spaced trails with decreased horizontal spacing

column_spacing = int(font_size * 1.5)  #decreased horizontal spacing
for i in range(SCREEN_WIDTH//column_spacing):
    x_pos = i * column_spacing
    #main trails 
    t = Trail(font_size, trail_speed, [x_pos, random.randint(-300, SCREEN_HEIGHT)], 8, font)
    trails.append(t)

#more trails in between with different speeds
for i in range(SCREEN_WIDTH//column_spacing):
    x_pos = i * column_spacing + column_spacing//2  
    #vary speed based on proximity
    random_speed = trail_speed * random.uniform(1.2, 1.8)  #faster for far away trails
    t = Trail(font_size, random_speed, [x_pos, random.randint(-200, SCREEN_HEIGHT)], 6, font)
    trails.append(t)

#more random trails with varying speeds          
for _ in range(40):  
    x_pos = random.randint(0, SCREEN_WIDTH)
    
    random_length = random.randint(3, 5)
    #shorter trails are faster
    speed_factor = 2.0 - (random_length / 5.0) * 0.8  
    random_speed = trail_speed * speed_factor * random.uniform(1.0, 1.8)
    random_y = random.randint(-300, SCREEN_HEIGHT)

    t = Trail(font_size, random_speed, [x_pos, random_y], random_length, font)
    trails.append(t)

color_wave = ColorWave()
running = True

while running:
    #initial black screen for first frame
    if pygame.time.get_ticks() < 100:
        screen.fill((0, 0, 0))

    if gradient_active:
        color_wave.update()

    # EVENTS
    for event in pygame.event.get():
        if event.type == pygame.QUIT:
            running = False
            break

        elif event.type == pygame.KEYDOWN:
            if event.key == pygame.K_ESCAPE:
                running = False
                break
#🦄 gradient keys
            elif event.key == pygame.K_F1:  #toggle gradient
                gradient_active = not gradient_active
                print(f"Gradient {'ON' if gradient_active else 'OFF'}")

            elif event.key == pygame.K_F2:  #switch X/Y mode
                color_wave.section_mode = "y" if color_wave.section_mode == "x" else "x"
                color_wave.create_sections()
#🦄

            # Process color change keys only if gradient is OFF
            if not gradient_active:

                #👻 color keys

                if event.key == pygame.K_RIGHT:
                    current_color = COLORS[(COLORS.index(current_color)+1)%len(COLORS)]
                    for t in trails:
                        for symbol in t.trail:

                            #depth effect to maintain a blurred background
                            depth = symbol["depth_factor"]
                            r = int(current_color[0] * depth)
                            g = int(current_color[1] * depth)
                            b = int(current_color[2] * depth)
                            symbol["color"] = (r, g, b)

                            #update the text with the new color
                            symbol["text"] = symbol["font"].render(symbol["symbol"], True, symbol["color"])
                            symbol["text"].set_alpha(symbol["alpha"])

                elif event.key == pygame.K_LEFT:
                    current_color = COLORS[(COLORS.index(current_color)-1)%len(COLORS)]
                    for t in trails:
                        for symbol in t.trail:
                            
                            depth = symbol["depth_factor"]
                            r = int(current_color[0] * depth)
                            g = int(current_color[1] * depth)
                            b = int(current_color[2] * depth)
                            symbol["color"] = (r, g, b)

                            symbol["text"] = symbol["font"].render(symbol["symbol"], True, symbol["color"])
                            symbol["text"].set_alpha(symbol["alpha"])
                #👻

            #add the character on key press 

            if event.unicode:
                #create a falling character at random position
                x_pos = random.randint(0, SCREEN_WIDTH)
                y_pos = 0  # Start at top of screen

                #special colors just for typed characters
                TYPED_COLORS = [
                    (0, 255, 0),      #0, green 
                    (0, 255, 255),    #1, cyan
                    (255, 0, 255),    #2, magenta
                    (255, 255, 0),    #3, yellow
                    (255, 128, 0),    #4, orange
                    (255, 0, 128),    #5, pink
                    (128, 255, 0),    #6, lime
                    (0, 128, 255)     #7, blue
                ]

                available_colors = [c for c in TYPED_COLORS 
                                        if sum(abs(c[i] - current_color[i]) for i in range(3)) > 200] #abs returns the absolute value of a number
                
                #if no colors are available use all of them
                if not available_colors:
                    available_colors = TYPED_COLORS

                typed_color = random.choice(available_colors)  

                #larger size for the pressed key 
                key_size = int(font_size * 2.5)  

                #single character trail 
                speed_factor = random.uniform(1.5, 2.5)  
                trail_length = random.randint(5, 8) 
                new_char = Trail(key_size, trail_speed * speed_factor,
                                [x_pos, y_pos], trail_length, font)  

                if len(new_char.trail) > 0:
                    new_char.trail[0]["symbol"] = event.unicode
                    new_char.trail[0]["color"] = typed_color
                    new_char.trail[0]["text"] = new_char.trail[0]["font"].render(
                        event.unicode, True, typed_color)

                    #make all characters in the trail the same as the pressed key
                    for i in range(1, len(new_char.trail)):
                        
                        size_factor = 1.0 - (i / len(new_char.trail) * 0.3)  
                        char_size = int(key_size * size_factor)  #

                        new_char.trail[i]["symbol"] = event.unicode

                        #make colors brighter at the top and fade down the trail for more Matrix effect
                        color_factor = 1.0 - (i / len(new_char.trail) * 0.5)

                        brightness_boost = 1.2  
                        r = max(0, min(255, int(typed_color[0] * color_factor * brightness_boost)))
                        g = max(0, min(255, int(typed_color[1] * color_factor * brightness_boost)))
                        b = max(0, min(255, int(typed_color[2] * color_factor * brightness_boost)))
                        new_char.trail[i]["color"] = (r, g, b)  

                        
                        new_char.trail[i]["font"] = pygame.font.SysFont("Consolas", char_size)
                        new_char.trail[i]["text"] = new_char.trail[i]["font"].render(
                            event.unicode, True, new_char.trail[i]["color"])

                        
                        blur_factor = i / len(new_char.trail)
                        if i == 0:
                            new_char.trail[i]["alpha"] = 255 

                        else:
                            
                            new_char.trail[i]["alpha"] = int(255 * (1 - blur_factor * 0.7))
                        new_char.trail[i]["text"].set_alpha(new_char.trail[i]["alpha"]) 

                new_char.is_typed = True

                trails.append(new_char)


    for t in trails:
#🦄 gradient colors

        if hasattr(t, 'is_typed') and t.is_typed:
            continue  #skip this trail entirely

        for symbol in t.trail:

            if gradient_active:

                color = color_wave.get_section_color(
                    symbol["position"][0],
                    symbol["position"][1],
                    color_wave.current_time
                )
                
            else:
                color = current_color  

            #only update color if it's significantly different 
            
            if abs(symbol["color"][0] - color[0]) > 10 or abs(symbol["color"][1] - color[1]) > 10:

                #depth effect to gradient color
                depth = symbol["depth_factor"]
                r = int(color[0] * depth)
                g = int(color[1] * depth)
                b = int(color[2] * depth)
                depth_color = (r, g, b)

                symbol["color"] = depth_color
                symbol["text"] = symbol["font"].render(symbol["symbol"], True, depth_color)
                # Only set alpha if needed
                if symbol["alpha"] < 255:
                    symbol["text"].set_alpha(symbol["alpha"])
            #🦄


    #move trail down and handle character changes/flickering
    current_time = time.time()

    #check for typed characters that need to be removed (after 3 seconds)

    trails_to_remove = []

    for i, t in enumerate(trails):
        
        if hasattr(t, 'is_typed') and t.is_typed:

            #how long the trail has existed?
            age = current_time - t.creation_time

            if age > 3.0:  
                trails_to_remove.append(i)

            elif age > 2.0:  
                
                fade_factor = float(3 - age)  

                for symbol in t.trail:

                    symbol["alpha"] = int(symbol["alpha"] * fade_factor)
                    symbol["text"].set_alpha(symbol["alpha"])  

    #remove expired typed characters 
    for i in sorted(trails_to_remove, reverse=True):
        trails.pop(i)

    for t in trails:
        for symbol in t.trail:

            # Set each letter's new position to be its old position + its speed
            symbol["position"] = (symbol["position"][0], symbol["position"][1] + t.speed)

            #💅
            if not (hasattr(t, 'is_typed') and t.is_typed) and symbol["should_change"]and current_time - symbol["last_change"] > symbol["change_time"]:

                symbol["symbol"] = random.choice(characters)
                symbol["last_change"] = current_time

                #timing calculation
                symbol["change_time"] = random.uniform(0.2, 1.0)
                symbol["text"] = symbol["font"].render(symbol["symbol"], True, symbol["color"])

                #set alpha if needed
                if symbol["alpha"] < 255:
                    symbol["text"].set_alpha(symbol["alpha"])

            #reset when off screen
            if symbol["position"][1] > SCREEN_HEIGHT:
                if hasattr(t, 'is_typed') and t.is_typed:
                    #for typed characters: they will be removed by the timer check above
                    pass

                else:                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            
                    #🐠reset for regular trails
                    symbol["position"] = (symbol["position"][0], random.randint(-100, -20))
                    symbol["symbol"] = random.choice(characters)

                    #just update the text
                    symbol["text"] = symbol["font"].render(symbol["symbol"], True, symbol["color"])



    # DRAWS

    overlay = pygame.Surface((SCREEN_WIDTH, SCREEN_HEIGHT), pygame.SRCALPHA)
    overlay.fill((0, 0, 0, 255))  #transparency for trail effect
    screen.blit(overlay, (0, 0))  

    #draw all letters in all trails
    for t in trails:
        for symbol in t.trail:
            screen.blit(symbol["text"],symbol["position"])


    pygame.display.flip()   #draw everything

    clock.tick(390) 
# END OF GAME LOOP

# We're outside the game loop, which means we want to end pygame!
pygame.quit()







