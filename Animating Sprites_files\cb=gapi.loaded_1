gapi.loaded_1(function(_){var window=this;
_.Cg=(window.gapi||{}).load;
_.lo=_.ye(_.Je,"rw",_.ze());
var mo=function(a,b){(a=_.lo[a])&&a.state<b&&(a.state=b)};var no=function(a){a=(a=_.lo[a])?a.oid:void 0;if(a){var b=_.ve.getElementById(a);b&&b.parentNode.removeChild(b);delete _.lo[a];no(a)}};_.oo=function(a){a=a.container;typeof a==="string"&&(a=document.getElementById(a));return a};_.po=function(a){var b=a.clientWidth;return"position:absolute;top:-10000px;width:"+(b?b+"px":a.style.width||"300px")+";margin:0px;border-style:none;"};
_.qo=function(a,b){var c={},d=a.xc(),e=b&&b.width,f=b&&b.height,h=b&&b.verticalAlign;h&&(c.verticalAlign=h);e||(e=d.width||a.width);f||(f=d.height||a.height);d.width=c.width=e;d.height=c.height=f;d=a.getIframeEl();e=a.getId();mo(e,2);a:{e=a.getSiteEl();c=c||{};var k;if(_.Je.oa&&(k=d.id)){f=(f=_.lo[k])?f.state:void 0;if(f===1||f===4)break a;no(k)}(f=e.nextSibling)&&f.dataset&&f.dataset.gapistub&&(e.parentNode.removeChild(f),e.style.cssText="");f=c.width;h=c.height;var l=e.style;l.textIndent="0";l.margin=
"0";l.padding="0";l.background="transparent";l.borderStyle="none";l.cssFloat="none";l.styleFloat="none";l.lineHeight="normal";l.fontSize="1px";l.verticalAlign="baseline";e=e.style;e.display="inline-block";d=d.style;d.position="static";d.left="0";d.top="0";d.visibility="visible";f&&(e.width=d.width=f+"px");h&&(e.height=d.height=h+"px");c.verticalAlign&&(e.verticalAlign=c.verticalAlign);k&&mo(k,3)}(k=b?b.title:null)&&a.getIframeEl().setAttribute("title",k);(b=b?b.ariaLabel:null)&&a.getIframeEl().setAttribute("aria-label",
b)};_.zo=function(a){var b=a.getSiteEl();b&&b.removeChild(a.getIframeEl())};_.Ao=function(a){a.where=_.oo(a);var b=a.messageHandlers=a.messageHandlers||{},c=function(e){_.qo(this,e)};b._ready=c;b._renderstart=c;var d=a.onClose;a.onClose=function(e){d&&d.call(this,e);_.zo(this)};a.onCreate=function(e){e=e.getIframeEl();e.style.cssText=_.po(e)}};
_.$i=function(a){var b=window;a=(a||b.location.href).match(RegExp(".*(\\?|#|&)usegapi=([^&#]+)"))||[];return"1"===decodeURIComponent(a[a.length-1]||"")};
_.Bo=function(a,b){a.T.where=b;return a};_.Co=function(){_.ck.apply(this,arguments)};_.y(_.Co,_.ck);
_.Do=_.ze();
_.Eo={};window.iframer=_.Eo;
var Go=function(a){var b=[new Fo];if(b.length===0)throw Error("j");if(b.map(function(c){if(c instanceof Fo)c=c.DY;else throw Error("j");return c}).every(function(c){return"data-gapiscan".indexOf(c)!==0}))throw Error("k`data-gapiscan");a.setAttribute("data-gapiscan","true")},Fo=function(){this.DY=Ho[0].toLowerCase()},Io,Jo,Ko,Lo,Mo,Qo,Ro;Fo.prototype.toString=function(){return this.DY};Io=function(a){if(_.xe.test(Object.keys))return Object.keys(a);var b=[],c;for(c in a)_.Ae(a,c)&&b.push(c);return b};
Jo={button:!0,div:!0,span:!0};Ko=function(a){var b=_.ye(_.Je,"sws",[]);return _.Om.call(b,a)>=0};Lo=function(a){return _.ye(_.Je,"watt",_.ze())[a]};Mo=function(a){return function(b,c){return a?_.tm()[c]||a[c]||"":_.tm()[c]||""}};_.No={callback:1,clientid:1,cookiepolicy:1,openidrealm:-1,includegrantedscopes:-1,requestvisibleactions:1,scope:1};_.Oo=!1;
_.Po=function(){if(!_.Oo){for(var a=document.getElementsByTagName("meta"),b=0;b<a.length;++b){var c=a[b].name.toLowerCase();if(_.vc(c,"google-signin-")){c=c.substring(14);var d=a[b].content;_.No[c]&&d&&(_.Do[c]=d)}}if(window.self!==window.top){a=document.location.toString();for(var e in _.No)_.No[e]>0&&(b=_.De(a,e,""))&&(_.Do[e]=b)}_.Oo=!0}e=_.ze();_.Be(_.Do,e);return e};Qo=function(a){var b;a.match(/^https?%3A/i)&&(b=decodeURIComponent(a));a=b?b:a;return _.$l(document,a)};
Ro=function(a){a=a||"canonical";for(var b=document.getElementsByTagName("link"),c=0,d=b.length;c<d;c++){var e=b[c],f=e.getAttribute("rel");if(f&&f.toLowerCase()==a&&(e=e.getAttribute("href"))&&(e=Qo(e))&&e.match(/^https?:\/\/[\w\-_\.]+/i)!=null)return e}return window.location.href};_.So=function(){return window.location.origin||window.location.protocol+"//"+window.location.host};_.To=function(a,b,c,d){return(a=typeof a=="string"?a:void 0)?Qo(a):Ro(d)};
_.Uo=function(a,b,c){a==null&&c&&(a=c.db,a==null&&(a=c.gwidget&&c.gwidget.db));return a||void 0};_.Vo=function(a,b,c){a==null&&c&&(a=c.ecp,a==null&&(a=c.gwidget&&c.gwidget.ecp));return a||void 0};_.Wo=function(a,b,c){return _.To(a,b,c,b.action?void 0:"publisher")};var Xo,Yo,Zo,cp,dp,ep,gp,fp;Xo={se:"0"};Yo={post:!0};Zo={style:"position:absolute;top:-10000px;width:450px;margin:0px;border-style:none"};cp="onPlusOne _ready _close _open _resizeMe _renderstart oncircled drefresh erefresh".split(" ");dp=_.ye(_.Je,"WI",_.ze());ep=["style","data-gapiscan"];
gp=function(a){for(var b=_.ze(),c=a.nodeName.toLowerCase().indexOf("g:")!=0,d=a.attributes.length,e=0;e<d;e++){var f=a.attributes[e],h=f.name,k=f.value;_.Om.call(ep,h)>=0||c&&h.indexOf("data-")!=0||k==="null"||"specified"in f&&!f.specified||(c&&(h=h.substr(5)),b[h.toLowerCase()]=k)}a=a.style;(c=fp(a&&a.height))&&(b.height=String(c));(a=fp(a&&a.width))&&(b.width=String(a));return b};
_.ip=function(a,b,c,d,e,f){if(c.rd)var h=b;else h=document.createElement("div"),b.dataset.gapistub=!0,h.style.cssText="position:absolute;width:450px;left:-10000px;",b.parentNode.insertBefore(h,b);f.siteElement=h;h.id||(h.id=_.hp(a));b=_.ze();b[">type"]=a;_.Be(c,b);a=_.xm(d,h,e);f.iframeNode=a;f.id=a.getAttribute("id")};_.hp=function(a){_.ye(dp,a,0);return"___"+a+"_"+dp[a]++};fp=function(a){var b=void 0;typeof a==="number"?b=a:typeof a==="string"&&(b=parseInt(a,10));return b};var Ho=_.dd(["data-"]),jp,kp,lp,mp,np=/(?:^|\s)g-((\S)*)(?:$|\s)/,op={plusone:!0,autocomplete:!0,profile:!0,signin:!0,signin2:!0};jp=_.ye(_.Je,"SW",_.ze());kp=_.ye(_.Je,"SA",_.ze());lp=_.ye(_.Je,"SM",_.ze());mp=_.ye(_.Je,"FW",[]);
var pp=function(a,b){return(typeof a==="string"?document.getElementById(a):a)||b},tp=function(a,b){var c;qp.ps0=(new Date).getTime();rp("ps0");a=pp(a,_.ve);var d=_.ve.documentMode;if(a.querySelectorAll&&(!d||d>8)){d=b?[b]:Io(jp).concat(Io(kp)).concat(Io(lp));for(var e=[],f=0;f<d.length;f++){var h=d[f];e.push(".g-"+h,"g\\:"+h)}d=a.querySelectorAll(e.join(","))}else d=a.getElementsByTagName("*");a=_.ze();for(e=0;e<d.length;e++){f=d[e];var k=f;h=b;var l=k.nodeName.toLowerCase(),m=void 0;if(k.hasAttribute("data-gapiscan"))h=
null;else{var n=l.indexOf("g:");n==0?m=l.substr(2):(n=(n=String(k.className||k.getAttribute("class")))&&np.exec(n))&&(m=n[1]);h=!m||!(jp[m]||kp[m]||lp[m])||h&&m!==h?null:m}h&&(op[h]||f.nodeName.toLowerCase().indexOf("g:")==0||Io(gp(f)).length!=0)&&(Go(f),_.ye(a,h,[]).push(f))}for(p in a)mp.push(p);qp.ps1=(new Date).getTime();rp("ps1");if(b=mp.join(":"))try{_.Ce.load(b,void 0)}catch(q){_.Sf.log(q);return}e=[];for(c in a){d=a[c];var p=0;for(b=d.length;p<b;p++)f=d[p],sp(c,f,gp(f),e,b)}};var up=function(a,b){var c=Lo(a);b&&c?(c(b),(c=b.iframeNode)&&c.setAttribute("data-gapiattached",!0)):_.Ce.load(a,function(){var d=Lo(a),e=b&&b.iframeNode,f=b&&b.userParams;e&&d?(d(b),e.setAttribute("data-gapiattached",!0)):(d=_.Ce[a].go,a=="signin2"?d(e,f):d(e&&e.parentNode,f))})},sp=function(a,b,c,d,e,f,h){switch(vp(b,a,f)){case 0:a=lp[a]?a+"_annotation":a;d={};d.iframeNode=b;d.userParams=c;up(a,d);break;case 1:if(b.parentNode){for(var k in c){if(f=_.Ae(c,k))f=c[k],f=!!f&&typeof f==="object"&&(!f.toString||
f.toString===Object.prototype.toString||f.toString===Array.prototype.toString);if(f)try{c[k]=_.Of(c[k])}catch(x){delete c[k]}}k=!0;c.dontclear&&(k=!1);delete c.dontclear;var l;f={};var m=l=a;a=="plus"&&c.action&&(l=a+"_"+c.action,m=a+"/"+c.action);(l=_.Ue("iframes/"+l+"/url"))||(l=":im_socialhost:/:session_prefix::im_prefix:_/widget/render/"+m+"?usegapi=1");for(n in Xo)f[n]=n+"/"+(c[n]||Xo[n])+"/";var n=_.$l(_.ve,l.replace(_.sm,Mo(f)));m="iframes/"+a+"/params/";f={};_.Be(c,f);(l=_.Ue("lang")||_.Ue("gwidget/lang"))&&
(f.hl=l);Yo[a]||(f.origin=_.So());f.exp=_.Ue(m+"exp");if(m=_.Ue(m+"location"))for(l=0;l<m.length;l++){var p=m[l];f[p]=_.ue.location[p]}switch(a){case "plus":case "follow":f.url=_.Wo(f.href,c,null);delete f.href;break;case "plusone":m=(m=c.href)?Qo(m):Ro();f.url=m;f.db=_.Uo(c.db,void 0,_.Ue());f.ecp=_.Vo(c.ecp,void 0,_.Ue());delete f.href;break;case "signin":f.url=Ro()}_.Je.ILI&&(f.iloader="1");delete f["data-onload"];delete f.rd;for(var q in Xo)f[q]&&delete f[q];f.gsrc=_.Ue("iframes/:source:");q=
_.Ue("inline/css");typeof q!=="undefined"&&e>0&&q>=e&&(f.ic="1");q=/^#|^fr-/;e={};for(var r in f)_.Ae(f,r)&&q.test(r)&&(e[r.replace(q,"")]=f[r],delete f[r]);r=_.Ue("iframes/"+a+"/params/si")=="q"?f:e;q=_.Po();for(var w in q)!_.Ae(q,w)||_.Ae(f,w)||_.Ae(e,w)||(r[w]=q[w]);w=[].concat(cp);r=_.Ue("iframes/"+a+"/methods");_.Nm(r)&&(w=w.concat(r));for(u in c)_.Ae(c,u)&&/^on/.test(u)&&(a!="plus"||u!="onconnect")&&(w.push(u),delete f[u]);delete f.callback;e._methods=w.join(",");var u=_.Zl(n,f,e);w=h||{};w.allowPost=
1;w.attributes=Zo;w.dontclear=!k;h={};h.userParams=c;h.url=u;h.type=a;_.ip(a,b,c,u,w,h);b=h.id;c=_.ze();c.id=b;c.userParams=h.userParams;c.url=h.url;c.type=h.type;c.state=1;_.lo[b]=c;b=h}else b=null;b&&((c=b.id)&&d.push(c),up(a,b))}},vp=function(a,b,c){if(a&&a.nodeType===1&&b){if(c)return 1;if(lp[b]){if(Jo[a.nodeName.toLowerCase()])return(a=a.innerHTML)&&a.replace(/^[\s\xa0]+|[\s\xa0]+$/g,"")?0:1}else{if(kp[b])return 0;if(jp[b])return 1}}return null};_.ye(_.Ce,"platform",{}).go=function(a,b){tp(a,b)};var wp=_.ye(_.Je,"perf",_.ze()),qp=_.ye(wp,"g",_.ze()),xp=_.ye(wp,"i",_.ze()),yp,zp,Ap,rp,Cp,Dp,Ep;_.ye(wp,"r",[]);yp=_.ze();zp=_.ze();Ap=function(a,b,c,d){yp[c]=yp[c]||!!d;_.ye(zp,c,[]);zp[c].push([a,b])};rp=function(a,b,c){var d=wp.r;typeof d==="function"?d(a,b,c):d.push([a,b,c])};Cp=function(a,b,c,d){if(b=="_p")throw Error("I");_.Bp(a,b,c,d)};_.Bp=function(a,b,c,d){Dp(b,c)[a]=d||(new Date).getTime();rp(a,b,c)};Dp=function(a,b){a=_.ye(xp,a,_.ze());return _.ye(a,b,_.ze())};
Ep=function(a,b,c){var d=null;b&&c&&(d=Dp(b,c)[a]);return d||qp[a]};(function(){function a(h){this.t={};this.tick=function(k,l,m){this.t[k]=[m!=void 0?m:(new Date).getTime(),l];if(m==void 0)try{window.console.timeStamp("CSI/"+k)}catch(n){}};this.getStartTickTime=function(){return this.t.start[0]};this.tick("start",null,h)}var b;if(window.performance)var c=(b=window.performance.timing)&&b.responseStart;var d=c>0?new a(c):new a;window.__gapi_jstiming__={Timer:a,load:d};if(b){var e=b.navigationStart;e>0&&c>=e&&(window.__gapi_jstiming__.srt=c-e)}if(b){var f=window.__gapi_jstiming__.load;
e>0&&c>=e&&(f.tick("_wtsrt",void 0,e),f.tick("wtsrt_","_wtsrt",c),f.tick("tbsd_","wtsrt_"))}try{b=null,window.chrome&&window.chrome.csi&&(b=Math.floor(window.chrome.csi().pageT),f&&e>0&&(f.tick("_tbnd",void 0,window.chrome.csi().startE),f.tick("tbnd_","_tbnd",e))),b==null&&window.gtbExternal&&(b=window.gtbExternal.pageT()),b==null&&window.external&&(b=window.external.pageT,f&&e>0&&(f.tick("_tbnd",void 0,window.external.startE),f.tick("tbnd_","_tbnd",e))),b&&(window.__gapi_jstiming__.pt=b)}catch(h){}})();if(window.__gapi_jstiming__){window.__gapi_jstiming__.DP={};window.__gapi_jstiming__.Aea=1;var Fp=function(a,b,c){var d=a.t[b],e=a.t.start;if(d&&(e||c))return d=a.t[b][0],e=c!=void 0?c:e[0],Math.round(d-e)},Gp=function(a,b,c){var d="";window.__gapi_jstiming__.srt&&(d+="&srt="+window.__gapi_jstiming__.srt,delete window.__gapi_jstiming__.srt);window.__gapi_jstiming__.pt&&(d+="&tbsrt="+window.__gapi_jstiming__.pt,delete window.__gapi_jstiming__.pt);try{window.external&&window.external.tran?d+="&tran="+
window.external.tran:window.gtbExternal&&window.gtbExternal.tran?d+="&tran="+window.gtbExternal.tran():window.chrome&&window.chrome.csi&&(d+="&tran="+window.chrome.csi().tran)}catch(p){}var e=window.chrome;if(e&&(e=e.loadTimes)&&typeof e==="function"&&(e=e())){e.wasFetchedViaSpdy&&(d+="&p=s");if(e.wasNpnNegotiated){d+="&npn=1";var f=e.npnNegotiatedProtocol;f&&(d+="&npnv="+(encodeURIComponent||escape)(f))}e.wasAlternateProtocolAvailable&&(d+="&apa=1")}var h=a.t,k=h.start;e=[];f=[];for(var l in h)if(l!=
"start"&&l.indexOf("_")!=0){var m=h[l][1];m?h[m]&&f.push(l+"."+Fp(a,l,h[m][0])):k&&e.push(l+"."+Fp(a,l))}delete h.start;if(b)for(var n in b)d+="&"+n+"="+b[n];(b=c)||(b="https:"==document.location.protocol?"https://csi.gstatic.com/csi":"http://csi.gstatic.com/csi");return[b,"?v=3","&s="+(window.__gapi_jstiming__.sn||"gwidget")+"&action=",a.name,f.length?"&it="+f.join(","):"",d,"&rt=",e.join(",")].join("")},Hp=function(a,b,c){a=Gp(a,b,c);if(!a)return"";b=new Image;var d=window.__gapi_jstiming__.Aea++;
window.__gapi_jstiming__.DP[d]=b;b.onload=b.onerror=function(){window.__gapi_jstiming__&&delete window.__gapi_jstiming__.DP[d]};b.src=a;b=null;return a};window.__gapi_jstiming__.report=function(a,b,c){var d=document.visibilityState,e="visibilitychange";d||(d=document.webkitVisibilityState,e="webkitvisibilitychange");if(d=="prerender"){var f=!1,h=function(){if(!f){b?b.prerender="1":b={prerender:"1"};if((document.visibilityState||document.webkitVisibilityState)=="prerender")var k=!1;else Hp(a,b,c),
k=!0;k&&(f=!0,document.removeEventListener(e,h,!1))}};document.addEventListener(e,h,!1);return""}return Hp(a,b,c)}};var Ip={g:"gapi_global",m:"gapi_module",w:"gwidget"},Jp=function(a,b){this.type=a?a=="_p"?"m":"w":"g";this.name=a;this.Bs=b};Jp.prototype.key=function(){switch(this.type){case "g":return this.type;case "m":return this.type+"."+this.Bs;case "w":return this.type+"."+this.name+this.Bs}};
var Kp=new Jp,Lp=navigator.userAgent.match(/iPhone|iPad|Android|PalmWebOS|Maemo|Bada/),Mp=_.ye(wp,"_c",_.ze()),Np=Math.random()<(_.Ue("csi/rate")||0),Pp=function(a,b,c){for(var d=new Jp(b,c),e=_.ye(Mp,d.key(),_.ze()),f=zp[a]||[],h=0;h<f.length;++h){var k=f[h],l=k[0],m=a,n=b,p=c;k=Ep(k[1],n,p);m=Ep(m,n,p);e[l]=k&&m?m-k:null}yp[a]&&Np&&(Op(Kp),Op(d))},Qp=function(a,b){b=b||[];for(var c=[],d=0;d<b.length;d++)c.push(a+b[d]);return c},Op=function(a){var b=_.ue.__gapi_jstiming__;b.sn=Ip[a.type];var c=new b.Timer(0);
a:{switch(a.type){case "g":var d="global";break a;case "m":d=a.Bs;break a;case "w":d=a.name;break a}d=void 0}c.name=d;d=!1;var e=a.key(),f=Mp[e];c.tick("_start",null,0);for(var h in f)c.tick(h,"_start",f[h]),d=!0;Mp[e]=_.ze();d&&(h=[],h.push("l"+(_.Ue("isPlusUser")?"1":"0")),d="m"+(Lp?"1":"0"),h.push(d),a.type=="m"?h.push("p"+a.Bs):a.type=="w"&&(e="n"+a.Bs,h.push(e),a.Bs=="0"&&h.push(d+e)),h.push("u"+(_.Ue("isLoggedIn")?"1":"0")),a=Qp("",h),a=Qp("abc_",a).join(","),b.report(c,{e:a}))};
Ap("blt","bs0","bs1");Ap("psi","ps0","ps1");Ap("rpcqi","rqe","rqd");Ap("bsprt","bsrt0","bsrt1");Ap("bsrqt","bsrt1","bsrt2");Ap("bsrst","bsrt2","bsrt3");Ap("mli","ml0","ml1");Ap("mei","me0","me1",!0);Ap("wcdi","wrs","wcdi");Ap("wci","wrs","wdc");Ap("wdi","wrs","wrdi");Ap("wdt","bs0","wrdt");Ap("wri","wrs","wrri",!0);Ap("wrt","bs0","wrrt");Ap("wji","wje0","wje1",!0);Ap("wjli","wjl0","wjl1");Ap("whi","wh0","wh1",!0);Ap("wai","waaf0","waaf1",!0);Ap("wadi","wrs","waaf1",!0);Ap("wadt","bs0","waaf1",!0);
Ap("wprt","wrt0","wrt1");Ap("wrqt","wrt1","wrt2");Ap("wrst","wrt2","wrt3",!0);Ap("fbprt","fsrt0","fsrt1");Ap("fbrqt","fsrt1","fsrt2");Ap("fbrst","fsrt2","fsrt3",!0);Ap("fdns","fdns0","fdns1");Ap("fcon","fcon0","fcon1");Ap("freq","freq0","freq1");Ap("frsp","frsp0","frsp1");Ap("fttfb","fttfb0","fttfb1");Ap("ftot","ftot0","ftot1",!0);var Rp=wp.r;if(typeof Rp!=="function"){for(var Sp;Sp=Rp.shift();)Pp.apply(null,Sp);wp.r=Pp};var Tp=["div"],Up="onload",Vp=!0,Wp=!0,Xp=function(a){return a},Yp=null,Zp=function(a){var b=_.Ue(a);return typeof b!=="undefined"?b:_.Ue("gwidget/"+a)},cq,dq,eq,fq,gq,hq,iq,oq,jq,pq,qq,rq,sq,tq,kq,mq,uq,lq,vq,wq,xq,yq,zq,Aq;Yp=_.Ue();_.Ue("gwidget");var $p=Zp("parsetags");Up=$p==="explicit"||$p==="onload"?$p:Up;var aq=Zp("google_analytics");typeof aq!=="undefined"&&(Vp=!!aq);var bq=Zp("data_layer");typeof bq!=="undefined"&&(Wp=!!bq);cq=function(){var a=this&&this.getId();a&&(_.Je.drw=a)};
dq=function(){_.Je.drw=null};eq=function(a){return function(b){var c=a;typeof b==="number"?c=b:typeof b==="string"&&(c=b.indexOf("px"),c!=-1&&(b=b.substring(0,c)),c=parseInt(b,10));return c}};fq=function(a){typeof a==="string"&&(a=window[a]);return typeof a==="function"?a:null};gq=function(){return Zp("lang")||"en-US"};
hq=function(a){if(!_.Ta.ub("attach")){var b={},c=_.Ta.ub("inline"),d;for(d in c)c.hasOwnProperty(d)&&(b[d]=c[d]);b.open=function(e){var f=e.xc().renderData.id;f=document.getElementById(f);if(!f)throw Error("J");return c.attach(e,f)};_.Ta.Fc("attach",b)}a.style="attach"};iq=function(){var a={};a.width=[eq(450)];a.height=[eq(24)];a.onready=[fq];a.lang=[gq,"hl"];a.iloader=[function(){return _.Je.ILI},"iloader"];return a}();
oq=function(a){var b={};b.Re=a[0];b.Pp=-1;b.bta="___"+b.Re+"_";b.xha="g:"+b.Re;b.lra="g-"+b.Re;b.fZ=[];b.config={};b.sy=[];b.w1={};b.BD={};var c=function(e){for(var f in e)if(_.Ae(e,f)){b.config[f]=[fq];b.sy.push(f);var h=e[f],k=null,l=null,m=null;typeof h==="function"?k=h:h&&typeof h==="object"&&(k=h.Zqa,l=h.CD,m=h.KN);m&&(b.sy.push(m),b.config[m]=[fq],b.w1[f]=m);k&&(b.config[f]=[k]);l&&(b.BD[f]=l)}},d=function(e){for(var f={},h=0;h<e.length;++h)f[e[h].toLowerCase()]=1;f[b.xha]=1;b.Dca=f};a[1]&&
(b.parameters=a[1]);(function(e){b.config=e;for(var f in iq)iq.hasOwnProperty(f)&&!b.config.hasOwnProperty(f)&&(b.config[f]=iq[f])})(a[2]||{});a[3]&&c(a[3]);a[4]&&d(a[4]);a[5]&&(b.Om=a[5]);b.Usa=a[6]===!0;b.aea=a[7];b.lha=a[8];b.Dca||d(Tp);b.zJ=function(e){b.Pp++;Cp("wrs",b.Re,String(b.Pp));var f=[],h=e.element,k=e.config,l=":"+b.Re;l==":plus"&&e.Ed&&e.Ed.action&&(l+="_"+e.Ed.action);var m=jq(b,k),n={};_.Be(_.Po(),n);for(var p in e.Ed)e.Ed[p]!=null&&(n[p]=e.Ed[p]);p={container:h.id,renderData:e.vea,
style:"inline",height:k.height,width:k.width};hq(p);b.Om&&(f[2]=p,f[3]=n,f[4]=m,b.Om("i",f));l=_.Ta.open(l,p,n,m);e=e.Y7;kq(l,k);lq(l,h);mq(b,l,e);nq(b.Re,b.Pp.toString(),l);f[5]=l;b.Om&&b.Om("e",f)};return b};
jq=function(a,b){for(var c={},d=a.sy.length-1;d>=0;--d){var e=a.sy[d],f=b[a.w1[e]||e]||b[e],h=b[e];h&&f!==h&&(f=function(l,m){return function(n){m.apply(this,arguments);l.apply(this,arguments)}}(f,h));f&&(c[e]=f)}for(var k in a.BD)a.BD.hasOwnProperty(k)&&(c[k]=pq(c[k]||function(){},a.BD[k]));c.drefresh=cq;c.erefresh=dq;return c};
pq=function(a,b){return function(c){var d=b(c);if(d){var e=c.href||null;if(Vp){if(window._gat)try{var f=window._gat._getTrackerByName("~0");f&&f._getAccount()!="UA-XXXXX-X"?f._trackSocial("Google",d,e):window._gaq&&window._gaq.push(["_trackSocial","Google",d,e])}catch(k){}if(window.ga&&window.ga.getAll)try{var h=window.ga.getAll();for(f=0;f<h.length;f++)h[f].send("social","Google",d,e)}catch(k){}}if(Wp&&window.dataLayer)try{window.dataLayer.push({event:"social",socialNetwork:"Google",socialAction:d,
socialTarget:e})}catch(k){}}a.call(this,c)}};qq=function(a){return _.Ln&&a instanceof _.Ln};rq=function(a){return qq(a)?"_renderstart":"renderstart"};sq=function(a){return qq(a)?"_ready":"ready"};tq=function(){return!0};kq=function(a,b){if(b.onready){var c=!1,d=function(){c||(c=!0,b.onready.call(null))};a.register(sq(a),d,tq);a.register(rq(a),d,tq)}};
mq=function(a,b,c){var d=a.Re,e=String(a.Pp),f=!1,h=function(){f||(f=!0,b.getIframeEl(),c&&Cp("wrdt",d,e),Cp("wrdi",d,e))};b.register(rq(b),h,tq);var k=!1;a=function(){k||(k=!0,h(),c&&Cp("wrrt",d,e),Cp("wrri",d,e))};b.register(sq(b),a,tq);qq(b)?b.register("widget-interactive-"+b.id,a,tq):_.Xf.register("widget-interactive-"+b.id,a);_.Xf.register("widget-csi-tick-"+b.id,function(l,m,n){l==="wdc"?Cp("wdc",d,e,n):l==="wje0"?Cp("wje0",d,e,n):l==="wje1"?Cp("wje1",d,e,n):l=="wh0"?_.Bp("wh0",d,e,n):l=="wh1"?
_.Bp("wh1",d,e,n):l=="wcdi"&&_.Bp("wcdi",d,e,n)})};uq=function(a){return typeof a=="number"?a+"px":a=="100%"?a:null};lq=function(a,b){var c=function(d){d=d||a;var e=uq(d.width);e&&b.style.width!=e&&(b.style.width=e);(d=uq(d.height))&&b.style.height!=d&&(b.style.height=d)};qq(a)?a.setParam("onRestyle",c):(a.register("ready",c,tq),a.register("renderstart",c,tq),a.register("resize",c,tq))};vq=function(a,b){for(var c in iq)if(iq.hasOwnProperty(c)){var d=iq[c][1];d&&!b.hasOwnProperty(d)&&(b[d]=a[d])}return b};
wq=function(a,b){var c={},d;for(d in a)a.hasOwnProperty(d)&&(c[a[d][1]||d]=(a[d]&&a[d][0]||Xp)(b[d.toLowerCase()],b,Yp));return c};xq=function(a){if(a=a.aea)for(var b=0;b<a.length;b++)(new Image).src=a[b]};yq=function(a,b){var c=b.userParams,d=b.siteElement;d||(d=(d=b.iframeNode)&&d.parentNode);if(d&&d.nodeType===1){var e=wq(a.config,c);a.fZ.push({element:d,config:e,Ed:vq(e,wq(a.parameters,c)),fsa:3,Y7:!!c["data-onload"],vea:b})}b=a.fZ;for(a=a.zJ;b.length>0;)a(b.shift())};
zq=function(a,b){a.Pp++;Cp("wrs",a.Re,String(a.Pp));var c=b.userParams,d=wq(a.config,c),e=[],f=b.iframeNode,h=b.siteElement,k=jq(a,d),l=wq(a.parameters,c);_.Be(_.Po(),l);l=vq(d,l);c=!!c["data-onload"];var m=_.an,n=_.ze();n.renderData=b;n.height=d.height;n.width=d.width;n.id=b.id;n.url=b.url;n.iframeEl=f;n.where=n.container=h;n.apis=["_open"];n.messageHandlers=k;n.messageHandlersFilter=_.dn;_.Ao(n);f=l;a.Om&&(e[2]=n,e[3]=f,e[4]=k,a.Om("i",e));k=m.attach(n);k.id=b.id;k.fM(k,n);kq(k,d);lq(k,h);mq(a,
k,c);nq(a.Re,a.Pp.toString(),k);e[5]=k;a.Om&&a.Om("e",e)};Aq=function(a,b){var c=b.url;a.lha||_.$i(c)?zq(a,b):_.Ta.open?yq(a,b):(0,_.Cg)("iframes",function(){yq(a,b)})};
_.Bq=function(a){var b=oq(a);xq(b);_.Vf(b.Re,function(d){Aq(b,d)});jp[b.Re]=!0;var c={va:function(d,e,f){var h=e||{};h.type=b.Re;e=h.type;delete h.type;var k=pp(d);if(k){d={};for(var l in h)_.Ae(h,l)&&(d[l.toLowerCase()]=h[l]);d.rd=1;(l=!!d.ri)&&delete d.ri;sp(e,k,d,[],0,l,f)}else _.Sf.log("gapi."+e+".render: missing element "+typeof d==="string"?d:"")},go:function(d){tp(d,b.Re)},hsa:function(){var d=_.ye(_.Je,"WI",_.ze()),e;for(e in d)delete d[e]}};a=function(){Up==="onload"&&c.go()};if(!Ko(b.Re)){if(!_.Tf())try{a()}catch(d){}_.Uf(a)}_.t("gapi."+
b.Re+".go",c.go);_.t("gapi."+b.Re+".render",c.va);return c};var Cq=function(){var a=window;return!!a.performance&&!!a.performance.getEntries},nq=function(a,b,c){if(Cq()){var d=function(){var f=!1;return function(){if(f)return!0;f=!0;return!1}}(),e=function(){d()||window.setTimeout(function(){var f=c.getIframeEl().src;var h=f.indexOf("#");h!=-1&&(f=f.substring(0,h));f=window.performance.getEntriesByName(f);f.length<1?f=null:(f=f[0],f=f.responseStart==0?null:f);if(f){h=Math.round(f.requestStart);var k=Math.round(f.responseStart),l=Math.round(f.responseEnd);
Cp("wrt0",a,b,Math.round(f.startTime));Cp("wrt1",a,b,h);Cp("wrt2",a,b,k);Cp("wrt3",a,b,l)}},1E3)};c.register(rq(c),e,tq);c.register(sq(c),e,tq)}};_.t("gapi.widget.make",_.Bq);
_.Ye=_.Ye||{};_.Ye.Cv=function(a,b,c){for(var d=[],e=2,f=arguments.length;e<f;++e)d.push(arguments[e]);return function(){for(var h=d.slice(),k=0,l=arguments.length;k<l;++k)h.push(arguments[k]);return b.apply(a,h)}};_.Ye.hB=function(a){var b,c,d={};for(b=0;c=a[b];++b)d[c]=c;return d};
_.Ye=_.Ye||{};
(function(){function a(c,d){return String.fromCharCode(d)}var b={0:!1,10:!0,13:!0,34:!0,39:!0,60:!0,62:!0,92:!0,8232:!0,8233:!0,65282:!0,65287:!0,65308:!0,65310:!0,65340:!0};_.Ye.escape=function(c,d){if(c){if(typeof c==="string")return _.Ye.WF(c);if(typeof c==="Array"){var e=0;for(d=c.length;e<d;++e)c[e]=_.Ye.escape(c[e])}else if(typeof c==="object"&&d){d={};for(e in c)c.hasOwnProperty(e)&&(d[_.Ye.WF(e)]=_.Ye.escape(c[e],!0));return d}}return c};_.Ye.WF=function(c){if(!c)return c;for(var d=[],e,f,
h=0,k=c.length;h<k;++h)e=c.charCodeAt(h),f=b[e],f===!0?d.push("&#",e,";"):f!==!1&&d.push(c.charAt(h));return d.join("")};_.Ye.Vsa=function(c){return c?c.replace(/&#([0-9]+);/g,a):c}})();
_.Ta.Ma={};_.Ta.Ma.Mi={};_.Ta.Ma.Mi.l7=function(a){try{return!!a.document}catch(b){}return!1};_.Ta.Ma.Mi.VT=function(a){var b=a.parent;return a!=b&&_.Ta.Ma.Mi.l7(b)?_.Ta.Ma.Mi.VT(b):a};_.Ta.Ma.Mi.ara=function(a){var b=a.userAgent||"";a=a.product||"";return b.indexOf("Opera")!=0&&b.indexOf("WebKit")==-1&&a=="Gecko"&&b.indexOf("rv:1.")>0};
_.Ta.Ma.Mi.Cv=function(a,b,c){for(var d=[],e=2,f=arguments.length;e<f;++e)d.push(arguments[e]);return function(){for(var h=d.slice(),k=0,l=arguments.length;k<l;++k)h.push(arguments[k]);return b.apply(a,h)}};
var Jq,Kq,Lq,Mq,Pq,Qq,Rq,Sq,Tq,Uq,Vq,Wq,Xq;Jq=function(){_.Xf.register("_noop_echo",function(){this.callback(_.Ta.f$(_.Ta.lm[this.f]))})};Kq=function(){window.setTimeout(function(){_.Xf.call("..","_noop_echo",_.Ta.Kda)},0)};Lq=function(a,b,c){var d=function(e){var f=Array.prototype.slice.call(arguments,0),h=f[f.length-1];if(typeof h==="function"){var k=h;f.pop()}f.unshift(b,a,k,c);_.Xf.call.apply(_.Xf,f)};d._iframe_wrapped_rpc_=!0;return d};
Mq=function(a){_.Ta.ZB[a]||(_.Ta.ZB[a]={},_.Xf.register(a,function(b,c){var d=this.f;if(!(typeof b!="string"||b in{}||d in{})){var e=this.callback,f=_.Ta.ZB[a][d],h;f&&Object.hasOwnProperty.call(f,b)?h=f[b]:Object.hasOwnProperty.call(_.Ta.Nq,a)&&(h=_.Ta.Nq[a]);if(h)return d=Array.prototype.slice.call(arguments,1),h._iframe_wrapped_rpc_&&e&&d.push(e),h.apply({},d)}_.Sf.error(['Unregistered call in window "',window.name,'" for method "',a,'", via proxyId "',b,'" from frame "',d,'".'].join(""));return null}));
return _.Ta.ZB[a]};_.Nq=function(){var a={};var b=window.location.href;var c=b.indexOf("?"),d=b.indexOf("#");b=(d===-1?b.substr(c+1):[b.substr(c+1,d-c-1),"&",b.substr(d+1)].join("")).split("&");c=window.decodeURIComponent?decodeURIComponent:unescape;d=b.length;for(var e=0;e<d;++e){var f=b[e].indexOf("=");if(f!==-1){var h=b[e].substring(0,f);f=b[e].substring(f+1);f=f.replace(/\+/g," ");try{a[h]=c(f)}catch(k){}}}return a};_.Oq=function(){return _.ue.location.origin||_.ue.location.protocol+"//"+_.ue.location.host};
Pq=function(a){_.Je.h=a};Qq=function(a){_.Je.bsh=a};Rq=function(a){var b=window.___jsl=window.___jsl||{};b[a]=b[a]||[];return b[a]};Sq=function(a){return typeof a==="object"&&/\[native code\]/.test(a.push)};
Tq=function(a,b,c){if(b&&typeof b==="object")for(var d in b)!Object.prototype.hasOwnProperty.call(b,d)||c&&d==="___goc"&&typeof b[d]==="undefined"||(a[d]&&b[d]&&typeof a[d]==="object"&&typeof b[d]==="object"&&!Sq(a[d])&&!Sq(b[d])?Tq(a[d],b[d]):b[d]&&typeof b[d]==="object"?(a[d]=Sq(b[d])?[]:{},Tq(a[d],b[d])):a[d]=b[d])};
Uq=function(){var a=window.location.hostname;return a?/(^|\.)(2mdn|ampproject|android|appspot|blogger|blogspot|chrome|chromium|doubleclick|gcpnode|ggpht|gmail|google|google-analytics|googleadservices|googleapis|googleapis-cn|googleoptimize|googlers|googlesource|googlesyndication|googletagmanager|googletagservices|googleusercontent|googlevideo|gstatic|tiltbrush|waze|withgoogle|youtube|ytimg)(\.com?|\.net|\.org)?(\.[a-z][a-z]|\.cat)?$/.test(a):!1};
Vq=function(a){try{var b=(new Function("return ("+a+"\n)"))()}catch(c){}if(typeof b==="object")return b;try{b=(new Function("return ({"+a+"\n})"))()}catch(c){}return b};
Wq=function(a,b){if(a&&!/^\s+$/.test(a)){for(;a.charCodeAt(a.length-1)==0;)a=a.substring(0,a.length-1);var c=a,d=Rq("dm");d.push(20);try{var e=window.JSON.parse(a)}catch(h){}if(typeof e==="object")return d.push(21),e;try{e=window.JSON.parse("{"+a+"}")}catch(h){}if(typeof e==="object")return d.push(22),e;a=a.replace(RegExp("([^\"',{}\\s]+?)\\s*:\\s*(.*?)[,}\\s]","g"),function(h,k,l){l=l.startsWith('"')?"%DOUBLE_QUOTE%"+l.substring(1):l;l=l.endsWith('"')?l.slice(0,-1)+"%DOUBLE_QUOTE%":l;return"%DOUBLE_QUOTE%"+
k+"%DOUBLE_QUOTE%:"+l});a=a.replace(/\\'/g,"%SINGLE_QUOTE%");a=a.replace(/"/g,'\\"');a=a.replace(/'/g,'"');a=a.replace(/%SINGLE_QUOTE%/g,"'");a=a.replace(/%DOUBLE_QUOTE%/g,'"');try{e=window.JSON.parse(a)}catch(h){}if(typeof e==="object")return d.push(23),e;try{e=window.JSON.parse("{"+a+"}")}catch(h){}if(typeof e==="object")return d.push(24),e;a=document.getElementsByTagName("script")||[];var f;a.length>0&&(f=a[0].nonce||a[0].getAttribute("nonce"));if(f&&f===b||!f&&Uq())if(e=Vq(c),d.push(25),typeof e===
"object")return e;return{}}};Xq=function(a,b){var c={___goc:void 0};a.length&&a[a.length-1]&&Object.hasOwnProperty.call(a[a.length-1],"___goc")&&typeof a[a.length-1].___goc==="undefined"&&(c=a.pop());Tq(c,b);a.push(c)};
_.Yq=function(a,b){var c;if(typeof a==="string"){var d=c={};a=a.split("/");for(var e=0,f=a.length;e<f-1;++e){var h={};d=d[a[e]]=h}d[a[e]]=b}else c=a;_.bi(!0);d=window.___gcfg;b=Rq("cu");a=window.___gu;d&&d!==a&&(Xq(b,d),window.___gu=d);d=Rq("cu");e=document.getElementsByTagName("script")||[];a=[];f=[];f.push.apply(f,Rq("us"));for(h=0;h<e.length;++h)for(var k=e[h],l=0;l<f.length;++l)k.src&&k.src.indexOf(f[l])==0&&a.push(k);a.length==0&&e.length>0&&e[e.length-1].src&&a.push(e[e.length-1]);for(e=0;e<
a.length;++e)a[e].getAttribute("gapi_processed")||(a[e].setAttribute("gapi_processed",!0),(f=a[e])?(h=f.nodeType,f=h==3||h==4?f.nodeValue:f.textContent||""):f=void 0,h=a[e].nonce||a[e].getAttribute("nonce"),(f=Wq(f,h))&&d.push(f));c&&Xq(b,c);a=Rq("cd");c=0;for(d=a.length;c<d;++c)Tq(_.bi(),a[c],!0);a=Rq("ci");c=0;for(d=a.length;c<d;++c)Tq(_.bi(),a[c],!0);c=0;for(d=b.length;c<d;++c)Tq(_.bi(),b[c],!0)};var Zq,$q=window.location.href,ar=$q.indexOf("?"),br=$q.indexOf("#");
Zq=(br===-1?$q.substr(ar+1):[$q.substr(ar+1,br-ar-1),"&",$q.substr(br+1)].join("")).split("&");for(var cr=window.decodeURIComponent?decodeURIComponent:unescape,dr=0,er=Zq.length;dr<er;++dr){var fr=Zq[dr].indexOf("=");if(fr!==-1){Zq[dr].substring(0,fr);var gr=Zq[dr].substring(fr+1);gr=gr.replace(/\+/g," ");try{cr(gr)}catch(a){}}};if(window.ToolbarApi)hr=window.ToolbarApi,hr.Ga=window.ToolbarApi.getInstance,hr.prototype=window.ToolbarApi.prototype,_.g=hr.prototype,_.g.openWindow=hr.prototype.openWindow,_.g.lQ=hr.prototype.closeWindow,_.g.o_=hr.prototype.setOnCloseHandler,_.g.UP=hr.prototype.canClosePopup,_.g.nZ=hr.prototype.resizeWindow;else{var hr=function(){};hr.Ga=function(){!ir&&window.external&&window.external.GTB_IsToolbar&&(ir=new hr);return ir};_.g=hr.prototype;_.g.openWindow=function(a){return window.external.GTB_OpenPopup&&
window.external.GTB_OpenPopup(a)};_.g.lQ=function(a){window.external.GTB_ClosePopupWindow&&window.external.GTB_ClosePopupWindow(a)};_.g.o_=function(a,b){window.external.GTB_SetOnCloseHandler&&window.external.GTB_SetOnCloseHandler(a,b)};_.g.UP=function(a){return window.external.GTB_CanClosePopup&&window.external.GTB_CanClosePopup(a)};_.g.nZ=function(a,b){return window.external.GTB_ResizeWindow&&window.external.GTB_ResizeWindow(a,b)};var ir=null;window.ToolbarApi=hr;window.ToolbarApi.getInstance=hr.Ga};var jr=/^[-_.0-9A-Za-z]+$/,kr={open:"open",onready:"ready",close:"close",onresize:"resize",onOpen:"open",onReady:"ready",onClose:"close",onResize:"resize",onRenderStart:"renderstart"},lr={onBeforeParentOpen:"beforeparentopen"},mr={onOpen:function(a){var b=a.xc();a.hh(b.container||b.element);return a},onClose:function(a){a.remove()}},nr=function(){_.Ta.dV++;return["I",_.Ta.dV,"_",(new Date).getTime()].join("")},or,pr,qr,tr,ur,vr,wr,yr,xr;_.Ta.Vn=function(a){var b=_.ze();_.Be(_.jm,b);_.Be(a,b);return b};
or=function(a){return a instanceof Array?a.join(","):a instanceof Object?_.Of(a):a};pr=function(a){var b=_.ci("googleapis.config/elog");if(b)try{b(a)}catch(c){}};qr=function(a){a&&a.match(jr)&&_.Yq("googleapis.config/gcv",a)};_.rr=function(a,b){b=b||{};for(var c in a)a.hasOwnProperty(c)&&(b[c]=a[c]);return b};
_.sr=function(a,b,c,d,e){var f=[],h;for(h in a)if(a.hasOwnProperty(h)){var k=b,l=c,m=a[h],n=d,p=Mq(h);p[k]=p[k]||{};n=_.Ta.Ma.Mi.Cv(n,m);m._iframe_wrapped_rpc_&&(n._iframe_wrapped_rpc_=!0);p[k][l]=n;f.push(h)}if(e)for(var q in _.Ta.Nq)_.Ta.Nq.hasOwnProperty(q)&&f.push(q);return f.join(",")};tr=function(a,b,c){var d={};if(a&&a._methods){a=a._methods.split(",");for(var e=0;e<a.length;e++){var f=a[e];d[f]=Lq(f,b,c)}}return d};
ur=function(a){if(a&&a.disableMultiLevelParentRelay)a=!1;else{var b;if(b=_.Eo&&_.Eo._open&&a.style!="inline"&&a.inline!==!0)a=a.container,b=!(a&&(typeof a=="string"&&document.getElementById(a)||document==(a.ownerDocument||a.document)));a=b}return a};vr=function(a,b){var c={};b=b.params||{};for(var d in a)d.charAt(0)=="#"&&(c[d.substring(1)]=a[d]),d.indexOf("fr-")==0&&(c[d.substring(3)]=a[d]),b[d]=="#"&&(c[d]=a[d]);for(var e in c)delete a["fr-"+e],delete a["#"+e],delete a[e];return c};
wr=function(a){if(a.charAt(0)==":"){a="iframes/"+a.substring(1);var b=_.ci(a);a={};_.Be(b,a);(b=a.url)&&(a.url=_.vm(b));a.params||(a.params={});return a}return{url:_.vm(a)}};yr=function(a){function b(){}b.prototype=xr.prototype;a.prototype=new b};
xr=function(a,b,c,d,e,f,h,k){this.config=wr(a);this.openParams=this.CB=b||{};this.params=c||{};this.methods=d;this.qD=!1;zr(this,b.style);this.callbacks={};Ar(this,function(){var l;(l=this.CB.style)&&_.Ta.Mw[l]?l=_.Ta.Mw[l]:l?(_.Sf.warn(['Missing handler for style "',l,'". Continuing with default handler.'].join("")),l=null):l=mr;if(l){if(typeof l==="function")var m=l(this);else{var n={};for(m in l){var p=l[m];n[m]=typeof p==="function"?_.Ta.Ma.Mi.Cv(l,p,this):p}m=n}for(var q in e)l=m[q],typeof l===
"function"&&Br(this,e[q],_.Ta.Ma.Mi.Cv(m,l))}f&&Br(this,"close",f)});this.Gk=this.ac=h;this.FJ=(k||[]).slice();h&&this.FJ.unshift(h.getId())};xr.prototype.xc=function(){return this.CB};xr.prototype.TG=function(){return this.params};xr.prototype.Qz=function(){return this.methods};xr.prototype.kd=function(){return this.Gk};
var zr=function(a,b){a.qD||((b=b&&!_.Ta.Mw[b]&&_.Ta.GF[b])?(a.FF=[],b(function(){a.qD=!0;for(var c=a.FF.length,d=0;d<c;++d)a.FF[d].call(a)})):a.qD=!0)},Ar=function(a,b){a.qD?b.call(a):a.FF.push(b)};xr.prototype.ze=function(a,b){Ar(this,function(){Br(this,a,b)})};var Br=function(a,b,c){a.callbacks[b]=a.callbacks[b]||[];a.callbacks[b].push(c)};xr.prototype.gp=function(a,b){Ar(this,function(){var c=this.callbacks[a];if(c)for(var d=c.length,e=0;e<d;++e)if(c[e]===b){c.splice(e,1);break}})};
xr.prototype.fi=function(a,b){var c=this.callbacks[a];if(c)for(var d=Array.prototype.slice.call(arguments,1),e=c.length,f=0;f<e;++f)try{var h=c[f].apply({},d)}catch(k){_.Sf.error(['Exception when calling callback "',a,'" with exception "',k.name,": ",k.message,'".'].join("")),pr(k)}return h};var Cr=function(a){return typeof a=="number"?{value:a,pG:a+"px"}:a=="100%"?{value:100,pG:"100%",TV:!0}:null};xr.prototype.send=function(a,b,c){_.Ta.LZ(this,a,b,c)};
xr.prototype.register=function(a,b){var c=this;c.ze(a,function(d){b.call(c,d)})};var Dr=function(a,b,c,d,e,f,h){var k=this;xr.call(this,a,b,c,d,kr,e,f,h);this.id=b.id||nr();this.jw=b.rpctoken&&String(b.rpctoken)||Math.round(_.Pi()*1E9);this.Waa=vr(this.params,this.config);this.dG={};Ar(this,function(){k.fi("open");_.rr(k.dG,k)})};yr(Dr);_.g=Dr.prototype;
_.g.hh=function(a,b){if(!this.config.url)return _.Sf.error("Cannot open iframe, empty URL."),this;var c=this.id;_.Ta.lm[c]=this;var d=_.rr(this.methods);d._ready=this.BB;d._close=this.close;d._open=this.OX;d._resizeMe=this.oZ;d._renderstart=this.IX;var e=this.Waa;this.jw&&(e.rpctoken=this.jw);e._methods=_.sr(d,c,"",this,!0);this.el=a=typeof a==="string"?document.getElementById(a):a;d={id:c};if(b){d.attributes=b;var f=b.style;if(typeof f==="string"){if(f){var h=[];f=f.split(";");for(var k=f.length,
l=0;l<k;++l){var m=f[l];if(m.length!=0||l+1!=k)m=m.split(":"),m.length==2&&m[0].match(/^[ a-zA-Z_-]+$/)&&m[1].match(/^[ +.%0-9a-zA-Z_-]+$/)?h.push(m.join(":")):_.Sf.error(['Iframe style "',f[l],'" not allowed.'].join(""))}h=h.join(";")}else h="";b.style=h}}this.xc().allowPost&&(d.allowPost=!0);this.xc().forcePost&&(d.forcePost=!0);d.queryParams=this.params;d.fragmentParams=e;d.paramsSerializer=or;this.ji=_.xm(this.config.url,a,d);a=this.ji.getAttribute("data-postorigin")||this.ji.src;_.Ta.lm[c]=this;
_.Xf.KC(this.id,this.jw);_.Xf.Pj(this.id,a);return this};_.g.Qh=function(a,b){this.dG[a]=b};_.g.getId=function(){return this.id};_.g.getIframeEl=function(){return this.ji};_.g.getSiteEl=function(){return this.el};_.g.setSiteEl=function(a){this.el=a};_.g.BB=function(a){var b=tr(a,this.id,"");this.Gk&&typeof this.methods._ready=="function"&&(a._methods=_.sr(b,this.Gk.getId(),this.id,this,!1),this.methods._ready(a));_.rr(a,this);_.rr(b,this);this.fi("ready",a)};
_.g.IX=function(a){this.fi("renderstart",a)};_.g.close=function(a){a=this.fi("close",a);delete _.Ta.lm[this.id];return a};_.g.remove=function(){var a=document.getElementById(this.id);a&&a.parentNode&&a.parentNode.removeChild(a)};
_.g.OX=function(a){var b=tr(a.params,this.id,a.proxyId);delete a.params._methods;a.openParams.anchor=="_parent"&&(a.openParams.anchor=this.el);if(ur(a.openParams))new Er(a.url,a.openParams,a.params,b,b._onclose,this,a.openedByProxyChain);else{var c=new Dr(a.url,a.openParams,a.params,b,b._onclose,this,a.openedByProxyChain),d=this;Ar(c,function(){var e={childId:c.getId()},f=c.dG;f._toclose=c.close;e._methods=_.sr(f,d.id,c.id,c,!1);b._onopen(e)})}};
_.g.oZ=function(a){if(this.fi("resize",a)===void 0&&this.ji){var b=Cr(a.width);b!=null&&(this.ji.style.width=b.pG);a=Cr(a.height);a!=null&&(this.ji.style.height=a.pG);this.ji.parentElement&&(b!=null&&b.TV||a!=null&&a.TV)&&(this.ji.parentElement.style.display="block")}};
var Er=function(a,b,c,d,e,f,h){var k=this;xr.call(this,a,b,c,d,lr,e,f,h);this.url=a;this.Jp=null;this.ZJ=nr();Ar(this,function(){k.fi("beforeparentopen");var l=_.rr(k.methods);l._onopen=k.Bda;l._ready=k.BB;l._onclose=k.zda;k.params._methods=_.sr(l,"..",k.ZJ,k,!0);l={};for(var m in k.params)l[m]=or(k.params[m]);_.Eo._open({url:k.config.url,openParams:k.CB,params:l,proxyId:k.ZJ,openedByProxyChain:k.FJ})})};yr(Er);Er.prototype.q$=function(){return this.Jp};
Er.prototype.Bda=function(a){this.Jp=a.childId;var b=tr(a,"..",this.Jp);_.rr(b,this);this.close=b._toclose;_.Ta.lm[this.Jp]=this;this.Gk&&this.methods._onopen&&(a._methods=_.sr(b,this.Gk.getId(),this.Jp,this,!1),this.methods._onopen(a))};Er.prototype.BB=function(a){var b=String(this.Jp),c=tr(a,"..",b);_.rr(a,this);_.rr(c,this);this.fi("ready",a);this.Gk&&this.methods._ready&&(a._methods=_.sr(c,this.Gk.getId(),b,this,!1),this.methods._ready(a))};
Er.prototype.zda=function(a){if(this.Gk&&this.methods._onclose)this.methods._onclose(a);else return a=this.fi("close",a),delete _.Ta.lm[this.Jp],a};
var Fr=function(a,b,c,d,e,f,h){xr.call(this,a,b,c,d,lr,f,h);this.id=b.id||nr();this.Yga=e;d._close=this.close;this.onClosed=this.BX;this.P1=0;Ar(this,function(){this.fi("beforeparentopen");var k=_.rr(this.methods);this.params._methods=_.sr(k,"..",this.ZJ,this,!0);k={};k.queryParams=this.params;a=_.om(_.ve,this.config.url,this.id,k);var l=e.openWindow(a);this.canAutoClose=function(m){m(e.UP(l))};e.o_(l,this);this.P1=l})};yr(Fr);
Fr.prototype.close=function(a){a=this.fi("close",a);this.Yga.lQ(this.P1);return a};Fr.prototype.BX=function(){this.fi("close")};_.Eo.send=function(a,b,c){_.Ta.LZ(_.Eo,a,b,c)};
(function(){function a(h){return _.Ta.Mw[h]}function b(h,k){_.Ta.Mw[h]=k}function c(h){h=h||{};h.height==="auto"&&(h.height=_.zm());var k=window&&hr&&hr.Ga();k?k.nZ(h.width||0,h.height||0):_.Eo&&_.Eo._resizeMe&&_.Eo._resizeMe(h)}function d(h){qr(h)}_.Ta.lm={};_.Ta.Mw={};_.Ta.GF={};_.Ta.dV=0;_.Ta.ZB={};_.Ta.Nq={};_.Ta.MB=null;_.Ta.LB=[];_.Ta.Kda=function(h){var k=!1;try{if(h!=null){var l=window.parent.frames[h.id];k=l.iframer.id==h.id&&l.iframes.openedId_(_.Eo.id)}}catch(m){}try{_.Ta.MB={origin:this.origin,
referer:this.referer,claimedOpenerId:h&&h.id,claimedOpenerProxyChain:h&&h.proxyChain||[],sameOrigin:k};for(h=0;h<_.Ta.LB.length;++h)_.Ta.LB[h](_.Ta.MB);_.Ta.LB=[]}catch(m){pr(m)}};_.Ta.f$=function(h){var k=h&&h.Gk,l=null;k&&(l={},l.id=k.getId(),l.proxyChain=h.FJ);return l};Jq();if(window.parent!=window){var e=_.Nq();e.gcv&&qr(e.gcv);var f=e.jsh;f&&Pq(f);_.rr(tr(e,"..",""),_.Eo);_.rr(e,_.Eo);Kq()}_.Ta.ub=a;_.Ta.Fc=b;_.Ta.Vfa=d;_.Ta.resize=c;_.Ta.C9=function(h){return _.Ta.GF[h]};_.Ta.lL=function(h,
k){_.Ta.GF[h]=k};_.Ta.mZ=c;_.Ta.qga=d;_.Ta.pA={};_.Ta.pA.get=a;_.Ta.pA.set=b;_.Ta.allow=function(h,k){Mq(h);_.Ta.Nq[h]=k||window[h]};_.Ta.eqa=function(h){delete _.Ta.Nq[h]};_.Ta.open=function(h,k,l,m,n,p){arguments.length==3?m={}:arguments.length==4&&typeof m==="function"&&(n=m,m={});var q=k.style==="bubble"&&hr?hr.Ga():null;return q?new Fr(h,k,l,m,q,n,p):ur(k)?new Er(h,k,l,m,n,p):new Dr(h,k,l,m,n,p)};_.Ta.close=function(h,k){_.Eo&&_.Eo._close&&_.Eo._close(h,k)};_.Ta.ready=function(h,k,l){arguments.length==
2&&typeof k==="function"&&(l=k,k={});var m=h||{};"height"in m||(m.height=_.zm());m._methods=_.sr(k||{},"..","",_.Eo,!0);_.Eo&&_.Eo._ready&&_.Eo._ready(m,l)};_.Ta.HT=function(h){_.Ta.MB?h(_.Ta.MB):_.Ta.LB.push(h)};_.Ta.Dda=function(h){return!!_.Ta.lm[h]};_.Ta.M9=function(){return["https://ssl.gstatic.com/gb/js/",_.ci("googleapis.config/gcv")].join("")};_.Ta.IY=function(h){var k={mouseover:1,mouseout:1};if(_.Eo._event)for(var l=0;l<h.length;l++){var m=h[l];m in k&&document.addEventListener(m,function(n){_.Eo._event({event:n.type,
timestamp:(new Date).getTime()})},!0)}};_.Ta.LZ=function(h,k,l,m){var n=this,p=[];l!==void 0&&p.push(l);m&&p.push(function(q){m.call(n,[q])});h[k]&&h[k].apply(h,p)};_.Ta.CROSS_ORIGIN_IFRAMES_FILTER=function(){return!0};_.Ta.k7=function(h,k,l){var m=Array.prototype.slice.call(arguments);_.Ta.HT(function(n){n.sameOrigin&&(m.unshift("/"+n.claimedOpenerId+"|"+window.location.protocol+"//"+window.location.host),_.Xf.call.apply(_.Xf,m))})};_.Ta.pea=function(h,k){_.Xf.register(h,k)};_.Ta.cga=Pq;_.Ta.TZ=
Qq;_.Ta.GW=pr;_.Ta.eV=_.Eo})();_.t("iframes.allow",_.Ta.allow);_.t("iframes.callSiblingOpener",_.Ta.k7);_.t("iframes.registerForOpenedSibling",_.Ta.pea);_.t("iframes.close",_.Ta.close);_.t("iframes.getGoogleConnectJsUri",_.Ta.M9);_.t("iframes.getHandler",_.Ta.ub);_.t("iframes.getDeferredHandler",_.Ta.C9);_.t("iframes.getParentInfo",_.Ta.HT);_.t("iframes.iframer",_.Ta.eV);_.t("iframes.open",_.Ta.open);_.t("iframes.openedId_",_.Ta.Dda);_.t("iframes.propagate",_.Ta.IY);_.t("iframes.ready",_.Ta.ready);_.t("iframes.resize",_.Ta.resize);
_.t("iframes.setGoogleConnectJsVersion",_.Ta.Vfa);_.t("iframes.setBootstrapHint",_.Ta.TZ);_.t("iframes.setJsHint",_.Ta.cga);_.t("iframes.setHandler",_.Ta.Fc);_.t("iframes.setDeferredHandler",_.Ta.lL);_.t("IframeBase",xr);_.t("IframeBase.prototype.addCallback",xr.prototype.ze);_.t("IframeBase.prototype.getMethods",xr.prototype.Qz);_.t("IframeBase.prototype.getOpenerIframe",xr.prototype.kd);_.t("IframeBase.prototype.getOpenParams",xr.prototype.xc);_.t("IframeBase.prototype.getParams",xr.prototype.TG);
_.t("IframeBase.prototype.removeCallback",xr.prototype.gp);_.t("Iframe",Dr);_.t("Iframe.prototype.close",Dr.prototype.close);_.t("Iframe.prototype.exposeMethod",Dr.prototype.Qh);_.t("Iframe.prototype.getId",Dr.prototype.getId);_.t("Iframe.prototype.getIframeEl",Dr.prototype.getIframeEl);_.t("Iframe.prototype.getSiteEl",Dr.prototype.getSiteEl);_.t("Iframe.prototype.openInto",Dr.prototype.hh);_.t("Iframe.prototype.remove",Dr.prototype.remove);_.t("Iframe.prototype.setSiteEl",Dr.prototype.setSiteEl);
_.t("Iframe.prototype.addCallback",Dr.prototype.ze);_.t("Iframe.prototype.getMethods",Dr.prototype.Qz);_.t("Iframe.prototype.getOpenerIframe",Dr.prototype.kd);_.t("Iframe.prototype.getOpenParams",Dr.prototype.xc);_.t("Iframe.prototype.getParams",Dr.prototype.TG);_.t("Iframe.prototype.removeCallback",Dr.prototype.gp);_.t("IframeProxy",Er);_.t("IframeProxy.prototype.getTargetIframeId",Er.prototype.q$);_.t("IframeProxy.prototype.addCallback",Er.prototype.ze);_.t("IframeProxy.prototype.getMethods",Er.prototype.Qz);
_.t("IframeProxy.prototype.getOpenerIframe",Er.prototype.kd);_.t("IframeProxy.prototype.getOpenParams",Er.prototype.xc);_.t("IframeProxy.prototype.getParams",Er.prototype.TG);_.t("IframeProxy.prototype.removeCallback",Er.prototype.gp);_.t("IframeWindow",Fr);_.t("IframeWindow.prototype.close",Fr.prototype.close);_.t("IframeWindow.prototype.onClosed",Fr.prototype.BX);_.t("iframes.util.getTopMostAccessibleWindow",_.Ta.Ma.Mi.VT);_.t("iframes.handlers.get",_.Ta.pA.get);_.t("iframes.handlers.set",_.Ta.pA.set);
_.t("iframes.resizeMe",_.Ta.mZ);_.t("iframes.setVersionOverride",_.Ta.qga);_.t("iframes.CROSS_ORIGIN_IFRAMES_FILTER",_.Ta.CROSS_ORIGIN_IFRAMES_FILTER);_.t("IframeBase.prototype.send",xr.prototype.send);_.t("IframeBase.prototype.register",xr.prototype.register);_.t("Iframe.prototype.send",Dr.prototype.send);_.t("Iframe.prototype.register",Dr.prototype.register);_.t("IframeProxy.prototype.send",Er.prototype.send);_.t("IframeProxy.prototype.register",Er.prototype.register);
_.t("IframeWindow.prototype.send",Fr.prototype.send);_.t("IframeWindow.prototype.register",Fr.prototype.register);_.t("iframes.iframer.send",_.Ta.eV.send);
var St=_.Ta.Fc,Tt={open:function(a){var b=_.oo(a.xc());return a.hh(b,{style:_.po(b)})},attach:function(a,b){var c=_.oo(a.xc()),d=b.id,e=b.getAttribute("data-postorigin")||b.src,f=/#(?:.*&)?rpctoken=(\d+)/.exec(e);f=f&&f[1];a.id=d;a.jw=f;a.el=c;a.ji=b;_.Ta.lm[d]=a;b=_.rr(a.methods);b._ready=a.BB;b._close=a.close;b._open=a.OX;b._resizeMe=a.oZ;b._renderstart=a.IX;_.sr(b,d,"",a,!0);_.Xf.KC(a.id,a.jw);_.Xf.Pj(a.id,e);c=_.Ta.Vn({style:_.po(c)});for(var h in c)Object.prototype.hasOwnProperty.call(c,h)&&
(h=="style"?a.ji.style.cssText=c[h]:a.ji.setAttribute(h,c[h]))}};Tt.onready=_.qo;Tt.onRenderStart=_.qo;Tt.close=_.zo;St("inline",Tt);
_.Ch=function(a,b){for(var c=1;c<arguments.length;c++){var d=arguments[c];if(_.hd(d)){var e=a.length||0,f=d.length||0;a.length=e+f;for(var h=0;h<f;h++)a[e+h]=d[h]}else a.push(d)}};_.Dh=function(a,b){b=b||a;for(var c=0,d=0,e={};d<a.length;){var f=a[d++],h=_.vb(f)?"o"+_.rh(f):(typeof f).charAt(0)+f;Object.prototype.hasOwnProperty.call(e,h)||(e[h]=!0,b[c++]=f)}b.length=c};_.Eh=function(a){for(var b in a)return!1;return!0};
_.Fh=function(a,b){a.src=_.ic(b);(b=_.Ec("script",a.ownerDocument))&&a.setAttribute("nonce",b)};_.Gh=function(a){for(var b=[],c=0,d=0;d<a.length;d++){var e=a.charCodeAt(d);e>255&&(b[c++]=e&255,e>>=8);b[c++]=e}return b};var Hh,Ih,Kh;Hh={};Ih=null;_.Jh=_.yd||_.zd||!_.Bh&&typeof _.Xa.atob=="function";_.Lh=function(a,b){b===void 0&&(b=0);Kh();b=Hh[b];for(var c=Array(Math.floor(a.length/3)),d=b[64]||"",e=0,f=0;e<a.length-2;e+=3){var h=a[e],k=a[e+1],l=a[e+2],m=b[h>>2];h=b[(h&3)<<4|k>>4];k=b[(k&15)<<2|l>>6];l=b[l&63];c[f++]=m+h+k+l}m=0;l=d;switch(a.length-e){case 2:m=a[e+1],l=b[(m&15)<<2]||d;case 1:a=a[e],c[f]=b[a>>2]+b[(a&3)<<4|m>>4]+l+d}return c.join("")};
_.Mh=function(a,b){function c(l){for(;d<a.length;){var m=a.charAt(d++),n=Ih[m];if(n!=null)return n;if(!_.wc(m))throw Error("w`"+m);}return l}Kh();for(var d=0;;){var e=c(-1),f=c(0),h=c(64),k=c(64);if(k===64&&e===-1)break;b(e<<2|f>>4);h!=64&&(b(f<<4&240|h>>2),k!=64&&b(h<<6&192|k))}};
Kh=function(){if(!Ih){Ih={};for(var a="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split(""),b=["+/=","+/","-_=","-_.","-_"],c=0;c<5;c++){var d=a.concat(b[c].split(""));Hh[c]=d;for(var e=0;e<d.length;e++){var f=d[e];Ih[f]===void 0&&(Ih[f]=e)}}}};
var ii;_.hi=function(a){this.Cc=a||{cookie:""}};_.g=_.hi.prototype;_.g.isEnabled=function(){if(!_.Xa.navigator.cookieEnabled)return!1;if(!this.isEmpty())return!0;this.set("TESTCOOKIESENABLED","1",{TI:60});if(this.get("TESTCOOKIESENABLED")!=="1")return!1;this.remove("TESTCOOKIESENABLED");return!0};
_.g.set=function(a,b,c){var d=!1;if(typeof c==="object"){var e=c.nsa;d=c.secure||!1;var f=c.domain||void 0;var h=c.path||void 0;var k=c.TI}if(/[;=\s]/.test(a))throw Error("z`"+a);if(/[;\r\n]/.test(b))throw Error("A`"+b);k===void 0&&(k=-1);this.Cc.cookie=a+"="+b+(f?";domain="+f:"")+(h?";path="+h:"")+(k<0?"":k==0?";expires="+(new Date(1970,1,1)).toUTCString():";expires="+(new Date(Date.now()+k*1E3)).toUTCString())+(d?";secure":"")+(e!=null?";samesite="+e:"")};
_.g.get=function(a,b){for(var c=a+"=",d=(this.Cc.cookie||"").split(";"),e=0,f;e<d.length;e++){f=_.yc(d[e]);if(f.lastIndexOf(c,0)==0)return f.slice(c.length);if(f==a)return""}return b};_.g.remove=function(a,b,c){var d=this.Dl(a);this.set(a,"",{TI:0,path:b,domain:c});return d};_.g.lg=function(){return ii(this).keys};_.g.Ze=function(){return ii(this).values};_.g.isEmpty=function(){return!this.Cc.cookie};_.g.Yb=function(){return this.Cc.cookie?(this.Cc.cookie||"").split(";").length:0};
_.g.Dl=function(a){return this.get(a)!==void 0};_.g.clear=function(){for(var a=ii(this).keys,b=a.length-1;b>=0;b--)this.remove(a[b])};ii=function(a){a=(a.Cc.cookie||"").split(";");for(var b=[],c=[],d,e,f=0;f<a.length;f++)e=_.yc(a[f]),d=e.indexOf("="),d==-1?(b.push(""),c.push(e)):(b.push(e.substring(0,d)),c.push(e.substring(d+1)));return{keys:b,values:c}};_.ji=new _.hi(typeof document=="undefined"?null:document);
_.ti={};_.ui=function(a){return _.ti[a||"token"]||null};
_.aj=function(a){a&&typeof a.dispose=="function"&&a.dispose()};_.bj=function(){this.Mg=this.Mg;this.Qo=this.Qo};_.bj.prototype.Mg=!1;_.bj.prototype.isDisposed=function(){return this.Mg};_.bj.prototype.dispose=function(){this.Mg||(this.Mg=!0,this.ua())};_.bj.prototype[Symbol.dispose]=function(){this.dispose()};_.dj=function(a,b){_.cj(a,_.bb(_.aj,b))};_.cj=function(a,b){a.Mg?b():(a.Qo||(a.Qo=[]),a.Qo.push(b))};_.bj.prototype.ua=function(){if(this.Qo)for(;this.Qo.length;)this.Qo.shift()()};
var lj;lj=function(a,b){for(var c in a)if(b.call(void 0,a[c],c,a))return!0;return!1};_.mj=function(a){this.src=a;this.ne={};this.jx=0};_.oj=function(a,b){this.type="function"==typeof _.nj&&a instanceof _.nj?String(a):a;this.currentTarget=this.target=b;this.defaultPrevented=this.Xv=!1};_.oj.prototype.stopPropagation=function(){this.Xv=!0};_.oj.prototype.preventDefault=function(){this.defaultPrevented=!0};_.pj=function(a,b){_.oj.call(this,a?a.type:"");this.relatedTarget=this.currentTarget=this.target=null;this.button=this.screenY=this.screenX=this.clientY=this.clientX=this.offsetY=this.offsetX=0;this.key="";this.charCode=this.keyCode=0;this.metaKey=this.shiftKey=this.altKey=this.ctrlKey=!1;this.state=null;this.SJ=!1;this.pointerId=0;this.pointerType="";this.timeStamp=0;this.Df=null;a&&this.init(a,b)};_.eb(_.pj,_.oj);
_.pj.prototype.init=function(a,b){var c=this.type=a.type,d=a.changedTouches&&a.changedTouches.length?a.changedTouches[0]:null;this.target=a.target||a.srcElement;this.currentTarget=b;b=a.relatedTarget;b||(c=="mouseover"?b=a.fromElement:c=="mouseout"&&(b=a.toElement));this.relatedTarget=b;d?(this.clientX=d.clientX!==void 0?d.clientX:d.pageX,this.clientY=d.clientY!==void 0?d.clientY:d.pageY,this.screenX=d.screenX||0,this.screenY=d.screenY||0):(this.offsetX=_.zd||a.offsetX!==void 0?a.offsetX:a.layerX,
this.offsetY=_.zd||a.offsetY!==void 0?a.offsetY:a.layerY,this.clientX=a.clientX!==void 0?a.clientX:a.pageX,this.clientY=a.clientY!==void 0?a.clientY:a.pageY,this.screenX=a.screenX||0,this.screenY=a.screenY||0);this.button=a.button;this.keyCode=a.keyCode||0;this.key=a.key||"";this.charCode=a.charCode||(c=="keypress"?a.keyCode:0);this.ctrlKey=a.ctrlKey;this.altKey=a.altKey;this.shiftKey=a.shiftKey;this.metaKey=a.metaKey;this.SJ=_.Bd?a.metaKey:a.ctrlKey;this.pointerId=a.pointerId||0;this.pointerType=
a.pointerType;this.state=a.state;this.timeStamp=a.timeStamp;this.Df=a;a.defaultPrevented&&_.pj.N.preventDefault.call(this)};_.pj.prototype.stopPropagation=function(){_.pj.N.stopPropagation.call(this);this.Df.stopPropagation?this.Df.stopPropagation():this.Df.cancelBubble=!0};_.pj.prototype.preventDefault=function(){_.pj.N.preventDefault.call(this);var a=this.Df;a.preventDefault?a.preventDefault():a.returnValue=!1};_.qj="closure_listenable_"+(Math.random()*1E6|0);_.rj=function(a){return!(!a||!a[_.qj])};var sj=0;var tj=function(a,b,c,d,e){this.listener=a;this.proxy=null;this.src=b;this.type=c;this.capture=!!d;this.Jf=e;this.key=++sj;this.dw=this.py=!1},uj=function(a){a.dw=!0;a.listener=null;a.proxy=null;a.src=null;a.Jf=null};_.mj.prototype.add=function(a,b,c,d,e){var f=a.toString();a=this.ne[f];a||(a=this.ne[f]=[],this.jx++);var h=vj(a,b,d,e);h>-1?(b=a[h],c||(b.py=!1)):(b=new tj(b,this.src,f,!!d,e),b.py=c,a.push(b));return b};_.mj.prototype.remove=function(a,b,c,d){a=a.toString();if(!(a in this.ne))return!1;var e=this.ne[a];b=vj(e,b,c,d);return b>-1?(uj(e[b]),Array.prototype.splice.call(e,b,1),e.length==0&&(delete this.ne[a],this.jx--),!0):!1};
_.wj=function(a,b){var c=b.type;if(!(c in a.ne))return!1;var d=_.ej(a.ne[c],b);d&&(uj(b),a.ne[c].length==0&&(delete a.ne[c],a.jx--));return d};_.mj.prototype.removeAll=function(a){a=a&&a.toString();var b=0,c;for(c in this.ne)if(!a||c==a){for(var d=this.ne[c],e=0;e<d.length;e++)++b,uj(d[e]);delete this.ne[c];this.jx--}return b};_.mj.prototype.Gq=function(a,b,c,d){a=this.ne[a.toString()];var e=-1;a&&(e=vj(a,b,c,d));return e>-1?a[e]:null};
_.mj.prototype.hasListener=function(a,b){var c=a!==void 0,d=c?a.toString():"",e=b!==void 0;return lj(this.ne,function(f){for(var h=0;h<f.length;++h)if(!(c&&f[h].type!=d||e&&f[h].capture!=b))return!0;return!1})};var vj=function(a,b,c,d){for(var e=0;e<a.length;++e){var f=a[e];if(!f.dw&&f.listener==b&&f.capture==!!c&&f.Jf==d)return e}return-1};var xj,yj,zj,Dj,Fj,Gj,Hj,Jj;xj="closure_lm_"+(Math.random()*1E6|0);yj={};zj=0;_.Bj=function(a,b,c,d,e){if(d&&d.once)return _.Aj(a,b,c,d,e);if(Array.isArray(b)){for(var f=0;f<b.length;f++)_.Bj(a,b[f],c,d,e);return null}c=_.Cj(c);return _.rj(a)?a.na(b,c,_.vb(d)?!!d.capture:!!d,e):Dj(a,b,c,!1,d,e)};
Dj=function(a,b,c,d,e,f){if(!b)throw Error("B");var h=_.vb(e)?!!e.capture:!!e,k=_.Ej(a);k||(a[xj]=k=new _.mj(a));c=k.add(b,c,d,h,f);if(c.proxy)return c;d=Fj();c.proxy=d;d.src=a;d.listener=c;if(a.addEventListener)_.vi||(e=h),e===void 0&&(e=!1),a.addEventListener(b.toString(),d,e);else if(a.attachEvent)a.attachEvent(Gj(b.toString()),d);else if(a.addListener&&a.removeListener)a.addListener(d);else throw Error("C");zj++;return c};
Fj=function(){var a=Hj,b=function(c){return a.call(b.src,b.listener,c)};return b};_.Aj=function(a,b,c,d,e){if(Array.isArray(b)){for(var f=0;f<b.length;f++)_.Aj(a,b[f],c,d,e);return null}c=_.Cj(c);return _.rj(a)?a.qr(b,c,_.vb(d)?!!d.capture:!!d,e):Dj(a,b,c,!0,d,e)};
_.Ij=function(a){if(typeof a==="number"||!a||a.dw)return!1;var b=a.src;if(_.rj(b))return b.AN(a);var c=a.type,d=a.proxy;b.removeEventListener?b.removeEventListener(c,d,a.capture):b.detachEvent?b.detachEvent(Gj(c),d):b.addListener&&b.removeListener&&b.removeListener(d);zj--;(c=_.Ej(b))?(_.wj(c,a),c.jx==0&&(c.src=null,b[xj]=null)):uj(a);return!0};Gj=function(a){return a in yj?yj[a]:yj[a]="on"+a};
Hj=function(a,b){if(a.dw)a=!0;else{b=new _.pj(b,this);var c=a.listener,d=a.Jf||a.src;a.py&&_.Ij(a);a=c.call(d,b)}return a};_.Ej=function(a){a=a[xj];return a instanceof _.mj?a:null};Jj="__closure_events_fn_"+(Math.random()*1E9>>>0);_.Cj=function(a){if(typeof a==="function")return a;a[Jj]||(a[Jj]=function(b){return a.handleEvent(b)});return a[Jj]};_.kj(function(a){Hj=a(Hj)});
_.Kj=function(a,b){var c=a.length-b.length;return c>=0&&a.indexOf(b,c)==c};_.Wd.prototype.O=_.pb(1,function(a){return _.Zd(this.Cc,a)});_.Lj=function(a,b,c,d,e){if(Array.isArray(b))for(var f=0;f<b.length;f++)_.Lj(a,b[f],c,d,e);else d=_.vb(d)?!!d.capture:!!d,c=_.Cj(c),_.rj(a)?a.Bc(b,c,d,e):a&&(a=_.Ej(a))&&(b=a.Gq(b,c,d,e))&&_.Ij(b)};_.Mj=function(){_.bj.call(this);this.rk=new _.mj(this);this.G6=this;this.KJ=null};_.eb(_.Mj,_.bj);_.Mj.prototype[_.qj]=!0;_.g=_.Mj.prototype;_.g.Zn=function(){return this.KJ};
_.g.WC=function(a){this.KJ=a};_.g.addEventListener=function(a,b,c,d){_.Bj(this,a,b,c,d)};_.g.removeEventListener=function(a,b,c,d){_.Lj(this,a,b,c,d)};
_.g.dispatchEvent=function(a){var b,c=this.Zn();if(c)for(b=[];c;c=c.Zn())b.push(c);c=this.G6;var d=a.type||a;if(typeof a==="string")a=new _.oj(a,c);else if(a instanceof _.oj)a.target=a.target||c;else{var e=a;a=new _.oj(d,c);_.gj(a,e)}e=!0;var f;if(b)for(f=b.length-1;!a.Xv&&f>=0;f--){var h=a.currentTarget=b[f];e=h.gu(d,!0,a)&&e}a.Xv||(h=a.currentTarget=c,e=h.gu(d,!0,a)&&e,a.Xv||(e=h.gu(d,!1,a)&&e));if(b)for(f=0;!a.Xv&&f<b.length;f++)h=a.currentTarget=b[f],e=h.gu(d,!1,a)&&e;return e};
_.g.ua=function(){_.Mj.N.ua.call(this);this.oK();this.KJ=null};_.g.na=function(a,b,c,d){return this.rk.add(String(a),b,!1,c,d)};_.g.qr=function(a,b,c,d){return this.rk.add(String(a),b,!0,c,d)};_.g.Bc=function(a,b,c,d){return this.rk.remove(String(a),b,c,d)};_.g.AN=function(a){return _.wj(this.rk,a)};_.g.oK=function(){this.rk&&this.rk.removeAll(void 0)};
_.g.gu=function(a,b,c){a=this.rk.ne[String(a)];if(!a)return!0;a=a.concat();for(var d=!0,e=0;e<a.length;++e){var f=a[e];if(f&&!f.dw&&f.capture==b){var h=f.listener,k=f.Jf||f.src;f.py&&this.AN(f);d=h.call(k,c)!==!1&&d}}return d&&!c.defaultPrevented};_.g.Gq=function(a,b,c,d){return this.rk.Gq(String(a),b,c,d)};_.g.hasListener=function(a,b){return this.rk.hasListener(a!==void 0?String(a):void 0,b)};
var Gr;Gr=function(){var a=_.Gc();if(_.Oc())return _.Vc(a);a=_.Lc(a);var b=_.Uc(a);return _.Nc()?b(["Version","Opera"]):_.Pc()?b(["Edge"]):_.Qc()?b(["Edg"]):_.Kc("Silk")?b(["Silk"]):_.Tc()?b(["Chrome","CriOS","HeadlessChrome"]):(a=a[2])&&a[1]||""};_.Hr=function(a){return _.Ac(Gr(),a)>=0};_.Jr=function(){return _.Rb&&_.Hc?_.Hc.mobile:!_.Ir()&&(_.Kc("iPod")||_.Kc("iPhone")||_.Kc("Android")||_.Kc("IEMobile"))};
_.Ir=function(){return _.Rb&&_.Hc?!_.Hc.mobile&&(_.Kc("iPad")||_.Kc("Android")||_.Kc("Silk")):_.Kc("iPad")||_.Kc("Android")&&!_.Kc("Mobile")||_.Kc("Silk")};_.Kr=function(){return!_.Jr()&&!_.Ir()};
var ct;ct=function(a,b,c){return arguments.length<=2?Array.prototype.slice.call(a,b):Array.prototype.slice.call(a,b,c)};_.dt=function(a,b,c,d){return Array.prototype.splice.apply(a,ct(arguments,1))};_.et=function(a,b,c){if(a!==null&&b in a)throw Error("h`"+b);a[b]=c};_.ft=function(a,b){var c=b||document;c.getElementsByClassName?a=c.getElementsByClassName(a)[0]:(c=document,a=a?(b||c).querySelector(a?"."+a:""):_.$d(c,"*",a,b)[0]||null);return a||null};
_.gt=function(a,b){b.parentNode&&b.parentNode.insertBefore(a,b.nextSibling)};_.ht=function(a,b,c){a&&!c&&(a=a.parentNode);for(c=0;a;){if(b(a))return a;a=a.parentNode;c++}return null};_.it=function(a){_.bj.call(this);this.vg=a;this.mc={}};_.eb(_.it,_.bj);var kt=[];_.it.prototype.na=function(a,b,c,d){return this.yv(a,b,c,d)};
_.it.prototype.yv=function(a,b,c,d,e){Array.isArray(b)||(b&&(kt[0]=b.toString()),b=kt);for(var f=0;f<b.length;f++){var h=_.Bj(a,b[f],c||this.handleEvent,d||!1,e||this.vg||this);if(!h)break;this.mc[h.key]=h}return this};_.it.prototype.qr=function(a,b,c,d){return lt(this,a,b,c,d)};var lt=function(a,b,c,d,e,f){if(Array.isArray(c))for(var h=0;h<c.length;h++)lt(a,b,c[h],d,e,f);else{b=_.Aj(b,c,d||a.handleEvent,e,f||a.vg||a);if(!b)return a;a.mc[b.key]=b}return a};
_.it.prototype.Bc=function(a,b,c,d,e){if(Array.isArray(b))for(var f=0;f<b.length;f++)this.Bc(a,b[f],c,d,e);else c=c||this.handleEvent,d=_.vb(d)?!!d.capture:!!d,e=e||this.vg||this,c=_.Cj(c),d=!!d,b=_.rj(a)?a.Gq(b,c,d,e):a?(a=_.Ej(a))?a.Gq(b,c,d,e):null:null,b&&(_.Ij(b),delete this.mc[b.key]);return this};_.it.prototype.removeAll=function(){_.Wb(this.mc,function(a,b){this.mc.hasOwnProperty(b)&&_.Ij(a)},this);this.mc={}};_.it.prototype.ua=function(){_.it.N.ua.call(this);this.removeAll()};
_.it.prototype.handleEvent=function(){throw Error("L");};
var Xu,Yu,Zu,$u,av,cv,dv,ev,fv,hv;_.Vu=function(a,b){for(var c in a)if(!(c in b)||a[c]!==b[c])return!1;for(var d in b)if(!(d in a))return!1;return!0};_.Wu=!1;Xu=function(a){try{_.Wu&&window.console&&window.console.log&&window.console.log(a)}catch(b){}};Yu=function(a){try{window.console&&window.console.warn&&window.console.warn(a)}catch(b){}};Zu=function(a,b){if(!a)return-1;if(a.indexOf)return a.indexOf(b,void 0);for(var c=0,d=a.length;c<d;c++)if(a[c]===b)return c;return-1};
$u=function(a,b){function c(){}if(!a)throw Error("O");if(!b)throw Error("P");c.prototype=b.prototype;a.prototype=new c;a.prototype.constructor=a};av=function(a){return Object.prototype.toString.call(a)==="[object Function]"};_.bv=function(a){var b={};if(a)for(var c in a)a.hasOwnProperty(c)&&(b[c]=a[c]);return b};cv=function(a){var b=location.hash;a=new RegExp("[&#]"+a+"=([^&]*)");b=decodeURIComponent(b);b=a.exec(b);return b==null?"":b[1].replace(/\+/g," ")};
dv=function(a,b,c){if(a.addEventListener)a.addEventListener(b,c,!1);else if(a.attachEvent)a.attachEvent("on"+b,c);else throw Error("Q`"+b);};ev={token:1,id_token:1};fv=function(){var a=navigator.userAgent.toLowerCase();return a.indexOf("msie")!=-1&&parseInt(a.split("msie")[1],10)==8};_.gv=window.JSON;hv=function(a){this.MN=a||[];this.qc={}};
hv.prototype.addEventListener=function(a,b){if(!(Zu(this.MN,a)>=0))throw Error("S`"+a);if(!av(b))throw Error("T`"+a);this.qc[a]||(this.qc[a]=[]);Zu(this.qc[a],b)<0&&this.qc[a].push(b)};hv.prototype.removeEventListener=function(a,b){if(!(Zu(this.MN,a)>=0))throw Error("S`"+a);av(b)&&this.qc[a]&&this.qc[a].length&&(b=Zu(this.qc[a],b),b>=0&&this.qc[a].splice(b,1))};
hv.prototype.dispatchEvent=function(a){var b=a.type;if(!(b&&Zu(this.MN,b)>=0))throw Error("U`"+b);if(this.qc[b]&&this.qc[b].length)for(var c=this.qc[b].length,d=0;d<c;d++)this.qc[b][d](a)};var iv,jv,lv,pv,qv,Hv,Iv,Kv,Lv,Nv,Rv,Sv,Tv,Xv;iv={};jv={};_.kv=function(){if(_.bd()&&!_.Hr("118"))return!1;var a=_.Tc()&&!_.Qc()&&!_.Sc(),b=_.Xc()||_.Kr();return"IdentityCredential"in window&&a&&b&&_.Hr("132")&&(_.Kr()||_.Xc())};lv={google:{fedcmConfigUrl:"https://accounts.google.com/o/fedcm/config.json",authServerUrl:"https://accounts.google.com/o/oauth2/auth",idpIFrameUrl:"https://accounts.google.com/o/oauth2/iframe"}};_.mv=function(a,b){if(a=lv[a])return a[b]};
_.nv=function(a,b){if(!a)throw Error("V");if(!b.authServerUrl)throw Error("W");if(!b.idpIFrameUrl)throw Error("X");lv[a]={authServerUrl:b.authServerUrl,idpIFrameUrl:b.idpIFrameUrl};b.fedcmConfigUrl?lv[a].fedcmConfigUrl=b.fedcmConfigUrl:a==="google"&&(lv[a].fedcmConfigUrl="https://accounts.google.com/o/fedcm/config.json")};_.ov=void 0;
pv=function(a){a.style.position="absolute";a.style.width="1px";a.style.height="1px";a.style.left="-9999px";a.style.top="-9999px";a.style.right="-9999px";a.style.bottom="-9999px";a.style.display="none";a.setAttribute("aria-hidden","true")};qv=function(){this.Ki=window;this.My=this.zn=this.Uv=this.xi=null};
qv.prototype.open=function(a,b,c,d){rv(this);this.Uv?(this.zn&&(this.zn(),this.zn=null),sv(this)):this.Uv="authPopup"+Math.floor(Math.random()*1E6+1);a:{this.xi=this.Ki.open(a,this.Uv,b);try{this.xi.focus();if(this.xi.closed||typeof this.xi.closed=="undefined")throw Error("Z");_.ov=this.xi}catch(e){d&&setTimeout(d,0);this.xi=null;break a}c&&(this.zn=c,tv(this))}};
var rv=function(a){try{if(a.xi==null||a.xi.closed)a.xi=null,a.Uv=null,sv(a),a.zn&&(a.zn(),a.zn=null)}catch(b){a.xi=null,a.Uv=null,sv(a)}},tv=function(a){a.My=window.setInterval(function(){rv(a)},300)},sv=function(a){a.My&&(window.clearInterval(a.My),a.My=null)};jv=jv||{};var uv=function(a,b){this.Xb=a;this.fI=b;this.Qc=null;this.uo=!1};uv.prototype.start=function(){if(!this.uo&&!this.Qc){var a=this;this.Qc=window.setTimeout(function(){a.clear();a.uo||(a.Xb(),a.uo=!0)},jv.TT(this.fI))}};
uv.prototype.clear=function(){this.Qc&&(window.clearTimeout(this.Qc),this.Qc=null)};var vv=function(a,b){var c=jv.ct;this.Xaa=jv.Ss;this.I1=c;this.Xb=a;this.fI=b;this.Qc=null;this.uo=!1;var d=this;this.J1=function(){document[d.Xaa]||(d.clear(),d.start())}};vv.prototype.start=function(){if(!this.uo&&!this.Qc){dv(document,this.I1,this.J1);var a=this;this.Qc=window.setTimeout(function(){a.clear();a.uo||(a.Xb(),a.uo=!0)},jv.TT(this.fI))}};
vv.prototype.clear=function(){var a=this.I1,b=this.J1,c=document;if(c.removeEventListener)c.removeEventListener(a,b,!1);else if(c.detachEvent)c.detachEvent("on"+a,b);else throw Error("R`"+a);this.Qc&&(window.clearTimeout(this.Qc),this.Qc=null)};jv.Ss=null;jv.ct=null;
jv.zba=function(){var a=document;typeof a.hidden!=="undefined"?(jv.Ss="hidden",jv.ct="visibilitychange"):typeof a.msHidden!=="undefined"?(jv.Ss="msHidden",jv.ct="msvisibilitychange"):typeof a.webkitHidden!=="undefined"&&(jv.Ss="webkitHidden",jv.ct="webkitvisibilitychange")};jv.zba();jv.W7=function(a,b){return jv.Ss&&jv.ct?new vv(a,b):new uv(a,b)};jv.TT=function(a){return Math.max(1,a-(new Date).getTime())};
var wv=function(a,b){document.cookie="G_ENABLED_IDPS="+a+";domain=."+b+";expires=Fri, 31 Dec 9999 12:00:00 GMT;path=/"},xv=function(){function a(){e[0]=1732584193;e[1]=4023233417;e[2]=2562383102;e[3]=271733878;e[4]=3285377520;n=m=0}function b(p){for(var q=h,r=0;r<64;r+=4)q[r/4]=p[r]<<24|p[r+1]<<16|p[r+2]<<8|p[r+3];for(r=16;r<80;r++)p=q[r-3]^q[r-8]^q[r-14]^q[r-16],q[r]=(p<<1|p>>>31)&4294967295;p=e[0];var w=e[1],u=e[2],x=e[3],A=e[4];for(r=0;r<80;r++){if(r<40)if(r<20){var C=x^w&(u^x);var F=1518500249}else C=
w^u^x,F=1859775393;else r<60?(C=w&u|x&(w|u),F=2400959708):(C=w^u^x,F=3395469782);C=((p<<5|p>>>27)&4294967295)+C+A+F+q[r]&4294967295;A=x;x=u;u=(w<<30|w>>>2)&4294967295;w=p;p=C}e[0]=e[0]+p&4294967295;e[1]=e[1]+w&4294967295;e[2]=e[2]+u&4294967295;e[3]=e[3]+x&4294967295;e[4]=e[4]+A&4294967295}function c(p,q){if(typeof p==="string"){p=unescape(encodeURIComponent(p));for(var r=[],w=0,u=p.length;w<u;++w)r.push(p.charCodeAt(w));p=r}q||(q=p.length);r=0;if(m==0)for(;r+64<q;)b(p.slice(r,r+64)),r+=64,n+=64;for(;r<
q;)if(f[m++]=p[r++],n++,m==64)for(m=0,b(f);r+64<q;)b(p.slice(r,r+64)),r+=64,n+=64}function d(){var p=[],q=n*8;m<56?c(k,56-m):c(k,64-(m-56));for(var r=63;r>=56;r--)f[r]=q&255,q>>>=8;b(f);for(r=q=0;r<5;r++)for(var w=24;w>=0;w-=8)p[q++]=e[r]>>w&255;return p}for(var e=[],f=[],h=[],k=[128],l=1;l<64;++l)k[l]=0;var m,n;a();return{reset:a,update:c,digest:d,Ti:function(){for(var p=d(),q="",r=0;r<p.length;r++)q+="0123456789ABCDEF".charAt(Math.floor(p[r]/16))+"0123456789ABCDEF".charAt(p[r]%16);return q}}},yv=
window.crypto,zv=!1,Av=0,Bv=1,Cv=0,Dv="",Ev=function(a){a=a||window.event;var b=a.screenX+a.clientX<<16;b+=a.screenY+a.clientY;b*=(new Date).getTime()%1E6;Bv=Bv*b%Cv;if(++Av==3)if(a=window,b=Ev,a.removeEventListener)a.removeEventListener("mousemove",b,!1);else if(a.detachEvent)a.detachEvent("onmousemove",b);else throw Error("R`mousemove");},Fv=function(a){var b=xv();b.update(a);return b.Ti()};zv=!!yv&&typeof yv.getRandomValues=="function";
zv||(Cv=(screen.width*screen.width+screen.height)*1E6,Dv=Fv(document.cookie+"|"+document.location+"|"+(new Date).getTime()+"|"+Math.random()),dv(window,"mousemove",Ev));iv=iv||{};iv.S3="ssIFrame_";
_.Gv=function(a,b,c){c=c===void 0?!1:c;this.Bb=a;if(!this.Bb)throw Error("$");a=_.mv(a,"idpIFrameUrl");if(!a)throw Error("aa");this.bV=a;if(!b)throw Error("ba");this.Tm=b;a=this.bV;b=document.createElement("a");b.setAttribute("href",a);a=[b.protocol,"//",b.hostname];b.protocol=="http:"&&b.port!=""&&b.port!="0"&&b.port!="80"?(a.push(":"),a.push(b.port)):b.protocol=="https:"&&b.port!=""&&b.port!="0"&&b.port!="443"&&(a.push(":"),a.push(b.port));this.OH=a.join("");this.afa=[location.protocol,"//",location.host].join("");
this.Tw=this.NH=this.yo=!1;this.XU=null;this.AB=[];this.Lr=[];this.fk={};this.zo=void 0;this.Es=c};_.g=_.Gv.prototype;_.g.show=function(){var a=this.zo;a.style.position="fixed";a.style.width="100%";a.style.height="100%";a.style.left="0px";a.style.top="0px";a.style.right="0px";a.style.bottom="0px";a.style.display="block";a.style.zIndex="9999999";a.style.overflow="hidden";a.setAttribute("aria-hidden","false")};_.g.hide=function(){pv(this.zo)};
_.g.fB=function(a){if(this.yo)a&&a(this);else{if(!this.zo){var b=iv.S3+this.Bb;var c=this.Bb;var d=location.hostname;var e,f=document.cookie.match("(^|;) ?G_ENABLED_IDPS=([^;]*)(;|$)");f&&f.length>2&&(e=f[2]);(f=e&&Zu(e.split("|"),c)>=0)?wv(e,d):wv(e?e+"|"+c:c,d);c=!f;var h=this.bV,k=this.afa;d=this.Tm;e=this.Es;e=e===void 0?!1:e;f=document.createElement("iframe");f.setAttribute("id",b);b=f.setAttribute;var l="allow-scripts allow-same-origin";document.requestStorageAccess&&av(document.requestStorageAccess)&&
(l+=" allow-storage-access-by-user-activation");b.call(f,"sandbox",l);f.setAttribute("allow","identity-credentials-get");pv(f);f.setAttribute("frame-border","0");b=[h,"#origin=",encodeURIComponent(k)];b.push("&rpcToken=");b.push(encodeURIComponent(d));c&&b.push("&clearCache=1");_.Wu&&b.push("&debug=1");e&&b.push("&supportBlocked3PCookies=1");document.body.appendChild(f);f.setAttribute("src",b.join(""));this.zo=f}a&&this.AB.push(a)}};_.g.dW=function(){return this.yo&&this.Tw};_.g.Wn=function(){return this.XU};
Hv=function(a){for(var b=0;b<a.AB.length;b++)a.AB[b](a);a.AB=[]};_.Jv=function(a,b,c,d){if(a.yo){if(a.yo&&a.NH)throw a="Failed to communicate with IDP IFrame due to unitialization error: "+a.Wn(),Xu(a),Error(a);Iv(a,{method:b,params:c},d)}else a.Lr.push({kp:{method:b,params:c},callback:d}),a.fB()};Iv=function(a,b,c){if(c){for(var d=b.id;!d||a.fk[d];)d=(new Date).getMilliseconds()+"-"+(Math.random()*1E6+1);b.id=d;a.fk[d]=c}b.rpcToken=a.Tm;a.zo.contentWindow.postMessage(_.gv.stringify(b),a.OH)};
Kv=function(a){if(a&&a.indexOf("::")>=0)throw Error("ca");};_.Gv.prototype.Aj=function(a,b,c,d,e,f,h,k,l){l=l===void 0?!1:l;Kv(f);b=_.bv(b);_.Jv(this,"getTokenResponse",{clientId:a,loginHint:c,request:b,sessionSelector:d,forceRefresh:h,skipCache:k,id:f,userInteracted:l},e)};_.Gv.prototype.dB=function(a,b,c,d,e){b=_.bv(b);_.Jv(this,"listIdpSessions",{clientId:a,request:b,sessionSelector:c,forceRefresh:e},d)};Lv=function(a,b,c){Kv(b.identifier);_.Jv(a,"getSessionSelector",b,c)};
_.Mv=function(a,b,c,d,e){Kv(b.identifier);_.Jv(a,"setSessionSelector",{domain:b.domain,crossSubDomains:b.crossSubDomains,policy:b.policy,id:b.id,hint:d,disabled:!!c},e)};Nv=function(a,b,c,d,e,f,h){b={clientId:b};c&&(b.pluginName=c);d&&(b.ackExtensionDate=d);b.useFedCm=e;f&&(b.fedCmEnabled=f);_.Jv(a,"monitorClient",b,h)};_.Gv.prototype.revoke=_.jb(8);_.Gv.prototype.rt=_.jb(10);iv.zA={};iv.LG=function(a){return iv.zA[a]};
iv.fB=function(a,b,c){c=c===void 0?!1:c;var d=iv.LG(a);if(!d){d=String;if(zv){var e=new window.Uint32Array(1);yv.getRandomValues(e);e=Number("0."+e[0])}else e=Bv,e+=parseInt(Dv.substr(0,20),16),Dv=Fv(Dv),e/=Cv+1.2089258196146292E24;d=new _.Gv(a,d(2147483647*e),c);iv.zA[a]=d}d.fB(b)};iv.E9=function(a){for(var b in iv.zA){var c=iv.LG(b);if(c&&c.zo&&c.zo.contentWindow==a.source&&c.OH==a.origin)return c}};iv.h$=function(a){for(var b in iv.zA){var c=iv.LG(b);if(c&&c.OH==a)return c}};iv=iv||{};
var Pv=function(){var a=[],b;for(b in _.Ov)a.push(_.Ov[b]);hv.call(this,a);this.om={};Xu("EventBus is ready.")};$u(Pv,hv);_.Ov={D5:"sessionSelectorChanged",qE:"sessionStateChanged",Qs:"authResult",M2:"displayIFrame"};Rv=function(a,b){var c=Qv;a&&b&&(c.om[a]||(c.om[a]=[]),Zu(c.om[a],b)<0&&c.om[a].push(b))};Sv=function(a){var b=Qv;a&&(b.om[a]||(b.om[a]=[]))};Tv=function(a,b,c){return b&&a.om[b]&&Zu(a.om[b],c)>=0};_.g=Pv.prototype;
_.g.fea=function(a){var b,c=!!a.source&&(a.source===_.ov||a.source.opener===window);if(b=c?iv.h$(a.origin):iv.E9(a)){try{var d=_.gv.parse(a.data)}catch(e){Xu("Bad event, an error happened when parsing data.");return}if(!c){if(!d||!d.rpcToken||d.rpcToken!=b.Tm){Xu("Bad event, no RPC token.");return}if(d.id&&!d.method){c=d;if(a=b.fk[c.id])delete b.fk[c.id],a(c.result,c.error);return}}d.method!="fireIdpEvent"?Xu("Bad IDP event, method unknown."):(a=d.params)&&a.type&&this.aV[a.type]?(d=this.aV[a.type],
c&&!d.L6?Xu("Bad IDP event. Source window cannot be a popup."):d.Ls&&!d.Ls.call(this,b,a)?Xu("Bad IDP event."):d.Jf.call(this,b,a)):Xu("Bad IDP event.")}else Xu("Bad event, no corresponding Idp Stub.")};_.g.Ffa=function(a,b){return Tv(this,a.Bb,b.clientId)};_.g.Efa=function(a,b){a=a.Bb;b=b.clientId;return!b||Tv(this,a,b)};_.g.X6=function(a,b){return Tv(this,a.Bb,b.clientId)};
_.g.oda=function(a,b){a.yo=!0;a.Tw=!!b.cookieDisabled;Hv(a);for(b=0;b<a.Lr.length;b++)Iv(a,a.Lr[b].kp,a.Lr[b].callback);a.Lr=[]};_.g.nda=function(a,b){b={error:b.error};a.yo=!0;a.NH=!0;a.XU=b;a.Lr=[];Hv(a)};_.g.aC=function(a,b){b.originIdp=a.Bb;this.dispatchEvent(b)};var Qv=new Pv,Uv=Qv,Vv={};Vv.idpReady={Jf:Uv.oda};Vv.idpError={Jf:Uv.nda};Vv.sessionStateChanged={Jf:Uv.aC,Ls:Uv.Ffa};Vv.sessionSelectorChanged={Jf:Uv.aC,Ls:Uv.Efa};Vv.authResult={Jf:Uv.aC,Ls:Uv.X6,L6:!0};Vv.displayIFrame={Jf:Uv.aC};
Qv.aV=Vv||{};dv(window,"message",function(a){Qv.fea.call(Qv,a)});
_.Wv=function(a,b){this.Pe=!1;if(!a)throw Error("da");var c=[],d;for(d in a)c.push(a[d]);hv.call(this,c);this.Cd=[location.protocol,"//",location.host].join("");this.Zd=b.crossSubDomains?b.domain||this.Cd:this.Cd;if(!b)throw Error("ea");if(!b.idpId)throw Error("fa");if(!_.mv(b.idpId,"authServerUrl")||!_.mv(b.idpId,"idpIFrameUrl"))throw Error("ga`"+b.idpId);this.Bb=b.idpId;this.Ob=void 0;this.g8=!!b.disableTokenRefresh;this.c9=!!b.forceTokenRefresh;this.Bga=!!b.skipTokenCache;this.Es=!!b.supportBlocked3PCookies;
b.pluginName&&(this.Uda=b.pluginName);b.ackExtensionDate&&(this.B6=b.ackExtensionDate);this.q1=b.useFedCm;this.L8=this.Es&&_.kv();this.setOptions(b);this.It=[];this.Tw=this.Bk=this.PV=!1;this.tj=void 0;this.aZ();this.Pd=void 0;var e=this,f=function(){Xu("Token Manager is ready.");if(e.It.length)for(var h=0;h<e.It.length;h++)e.It[h].call(e);e.PV=!0;e.It=[]};iv.fB(this.Bb,function(h){e.Pd=h;h.yo&&h.NH?(e.Bk=!0,e.tj=h.Wn(),e.Cr(e.tj)):(e.Tw=h.dW(),e.Ob?Nv(e.Pd,e.Ob,e.Uda,e.B6,e.q1,e.L8,function(k){var l=
!!k.validOrigin,m=!!k.blocked,n=!!k.suppressed;k.invalidExtension?(e.tj={error:"Invalid value for ack_extension_date. Please refer to [Migration Guide](https://developers.google.com/identity/gsi/web/guides/gis-migration) for more information."},e.Bk=!0,e.Cr(e.tj)):l?m?n?(Yu("You have created a new client application that uses libraries for user authentication or authorization that are deprecated. New clients must use the new libraries instead. See the [Migration Guide](https://developers.google.com/identity/gsi/web/guides/gis-migration) for more information."),
Rv(e.Bb,e.Ob),f()):(e.tj={error:"You have created a new client application that uses libraries for user authentication or authorization that are deprecated. New clients must use the new libraries instead. See the [Migration Guide](https://developers.google.com/identity/gsi/web/guides/gis-migration) for more information."},e.Bk=!0,e.Cr(e.tj)):(Yu("Your client application uses libraries for user authentication or authorization that are deprecated. See the [Migration Guide](https://developers.google.com/identity/gsi/web/guides/gis-migration) for more information."),
Rv(e.Bb,e.Ob),f()):(e.tj={error:"Not a valid origin for the client: "+e.Cd+" has not been registered for client ID "+e.Ob+". Please go to https://console.developers.google.com/ and register this origin for your project's client ID."},e.Bk=!0,e.Cr(e.tj))}):(Sv(e.Bb),f()))},this.Es)};$u(_.Wv,hv);_.g=_.Wv.prototype;_.g.setOptions=function(){};_.g.aZ=function(){};_.g.Cr=function(){};_.g.dW=function(){return this.Tw};_.g.Wn=function(){return this.tj};Xv=function(a,b,c){return function(){b.apply(a,c)}};
_.Yv=function(a,b,c){if(a.PV)b.apply(a,c);else{if(a.Bk)throw a.tj;a.It.push(Xv(a,b,c))}};_.Wv.prototype.cQ=_.jb(11);_.Wv.prototype.rt=_.jb(9);_.$v=function(a,b){_.Wv.call(this,a,b);this.sY=new qv;this.Kk=this.Uo=null;Zv(this)};$u(_.$v,_.Wv);_.$v.prototype.setOptions=function(){};
var aw=function(a,b){a.Me={crossSubDomains:!!b.crossSubDomains,id:b.sessionSelectorId,domain:a.Zd};b.crossSubDomains&&(a.Me.policy=b.policy)},bw=function(a,b){if(!b.authParameters)throw Error("ha");if(!b.authParameters.scope)throw Error("ia");if(!b.authParameters.response_type)throw Error("ja");a.qn=b.authParameters;a.qn.redirect_uri||(a.qn.redirect_uri=[location.protocol,"//",location.host,location.pathname].join(""));a.Jj=_.bv(b.rpcAuthParameters||a.qn);if(!a.Jj.scope)throw Error("ka");if(!a.Jj.response_type)throw Error("la");
a:{var c=a.Jj.response_type.split(" ");for(var d=0,e=c.length;d<e;d++)if(c[d]&&!ev[c[d]]){c=!0;break a}c=!1}if(c)throw Error("ma");if(b.enableSerialConsent||b.enableGranularConsent)a.qn.enable_granular_consent=!0,a.Jj.enable_serial_consent=!0;b.authResultIdentifier&&(a.Y6=b.authResultIdentifier);b.spec_compliant&&(a.Jj.spec_compliant=b.spec_compliant)};
_.$v.prototype.aZ=function(){var a=this;Qv.addEventListener(_.Ov.D5,function(b){a.Pe&&a.Me&&b.originIdp==a.Bb&&!b.crossSubDomains==!a.Me.crossSubDomains&&b.domain==a.Me.domain&&b.id==a.Me.id&&a.KX(b)});Qv.addEventListener(_.Ov.qE,function(b){a.Pe&&b.originIdp==a.Bb&&b.clientId==a.Ob&&a.LX(b)});Qv.addEventListener(_.Ov.Qs,function(b){_.ov=void 0;a.Pe&&b.originIdp==a.Bb&&b.clientId==a.Ob&&b.id==a.Hk&&(a.Uo&&(window.clearTimeout(a.Uo),a.Uo=null),a.Hk=void 0,a.Po(b))});Qv.addEventListener(_.Ov.M2,function(b){a.Pe&&
b.originIdp==a.Bb&&(b.hide?a.Pd.hide():a.Pd.show())})};_.$v.prototype.KX=function(){};_.$v.prototype.LX=function(){};_.$v.prototype.Po=function(){};var dw=function(a,b){cw(a);a.g8||(a.Kk=jv.W7(function(){a.Aj(!0)},b-3E5),navigator.onLine&&a.Kk.start())},cw=function(a){a.Kk&&(a.Kk.clear(),a.Kk=null)},Zv=function(a){var b=window;fv()&&(b=document.body);dv(b,"online",function(){a.Kk&&a.Kk.start()});dv(b,"offline",function(){a.Kk&&a.Kk.clear()})};_.$v.prototype.Aj=function(){};_.$v.prototype.nX=_.jb(12);
_.$v.prototype.ica=function(a,b){if(!this.Ob)throw Error("qa");this.Pd.dB(this.Ob,this.Jj,this.Me,a,b)};_.$v.prototype.dB=function(a,b){_.Yv(this,this.ica,[a,b])};_.fw=function(a){this.Ge=void 0;this.Nh=!1;this.Ur=void 0;_.$v.call(this,ew,a)};$u(_.fw,_.$v);var ew={rO:"noSessionBound",bt:"userLoggedOut",X1:"activeSessionChanged",qE:"sessionStateChanged",e6:"tokenReady",d6:"tokenFailed",Qs:"authResult",ERROR:"error"};
_.fw.prototype.setOptions=function(a){if(!a.clientId)throw Error("ra");this.Ob=a.clientId;this.Da=a.id;aw(this,a);bw(this,a)};_.fw.prototype.Cr=function(a){this.dispatchEvent({type:ew.ERROR,error:"idpiframe_initialization_failed",details:a.error,idpId:this.Bb})};var gw=function(a){cw(a);a.Ur=void 0;a.CI=void 0};_.g=_.fw.prototype;
_.g.KX=function(a){var b=a.newValue||{};if(this.Ge!=b.hint||this.Nh!=!!b.disabled){a=this.Ge;var c=!this.Ge||this.Nh;gw(this);this.Ge=b.hint;this.Nh=!!b.disabled;(b=!this.Ge||this.Nh)&&!c?this.dispatchEvent({type:ew.bt,idpId:this.Bb}):b||(a!=this.Ge&&this.dispatchEvent({type:ew.X1,idpId:this.Bb}),this.Ge&&this.Aj())}};
_.g.LX=function(a){this.Nh||(this.Ge?a.user||this.Ur?a.user==this.Ge&&(this.Ur?a.sessionState?this.Ur=a.sessionState:(gw(this),this.dispatchEvent({type:ew.bt,idpId:this.Bb})):a.sessionState&&(this.Ur=a.sessionState,this.Aj())):this.Aj():this.dispatchEvent({type:ew.qE,idpId:this.Bb}))};_.g.Po=function(a){this.dispatchEvent({type:ew.Qs,authResult:a.authResult})};_.g.wu=_.jb(14);_.g.pu=function(a){_.Yv(this,this.zG,[a])};_.g.zG=function(a){Lv(this.Pd,this.Me,a)};
_.g.pD=function(a,b,c,d){d=d===void 0?!1:d;if(!a)throw Error("sa");gw(this);this.Ge=a;this.Nh=!1;b&&_.Mv(this.Pd,this.Me,!1,this.Ge);this.Pe=!0;this.Aj(c,!0,d)};_.g.start=function(){_.Yv(this,this.Kw,[])};
_.g.Kw=function(){var a=this.Ob==cv("client_id")?cv("login_hint"):void 0;var b=this.Ob==cv("client_id")?cv("state"):void 0;this.mJ=b;if(a)window.history.replaceState?window.history.replaceState(null,document.title,window.location.href.split("#")[0]):window.location.href.hash="",this.pD(a,!0,!0,!0);else{var c=this;this.pu(function(d){c.Pe=!0;d&&d.hint?(gw(c),c.Ge=d.hint,c.Nh=!!d.disabled,c.Nh?c.dispatchEvent({type:ew.bt,idpId:c.Bb}):c.pD(d.hint)):(gw(c),c.Ge=void 0,c.Nh=!(!d||!d.disabled),c.dispatchEvent({type:ew.rO,
autoOpenAuthUrl:!c.Nh,idpId:c.Bb}))})}};_.g.Y8=function(){var a=this;this.pu(function(b){b&&b.hint?b.disabled?a.dispatchEvent({type:ew.bt,idpId:a.Bb}):a.Aj(!0):a.dispatchEvent({type:ew.rO,idpId:a.Bb})})};_.g.PS=function(){_.Yv(this,this.Y8,[])};
_.g.Aj=function(a,b,c){var d=this;this.Pd.Aj(this.Ob,this.Jj,this.Ge,this.Me,function(e,f){(f=f||e.error)?f=="user_logged_out"?(gw(d),d.dispatchEvent({type:ew.bt,idpId:d.Bb})):(d.CI=null,d.dispatchEvent({type:ew.d6,idpId:d.Bb,error:f})):(d.CI=e,d.Ur=e.session_state,dw(d,e.expires_at),e.idpId=d.Bb,b&&d.mJ&&(e.state=d.mJ,d.mJ=void 0),d.dispatchEvent({type:ew.e6,idpId:d.Bb,response:e}))},this.Da,a,!1,c===void 0?!1:c)};_.g.revoke=_.jb(7);_.g.uZ=_.jb(15);
_.hw=function(a){this.rn=null;_.$v.call(this,{},a);this.Pe=!0};$u(_.hw,_.$v);_.g=_.hw.prototype;_.g.setOptions=function(a){if(!a.clientId)throw Error("ra");this.Ob=a.clientId;this.Da=a.id;aw(this,a);bw(this,a)};_.g.Cr=function(a){this.rn&&(this.rn({authResult:{error:"idpiframe_initialization_failed",details:a.error}}),this.rn=null)};_.g.Po=function(a){if(this.rn){var b=this.rn;this.rn=null;b(a)}};_.g.wu=_.jb(13);_.g.pu=function(a){this.Bk?a(this.Wn()):_.Yv(this,this.zG,[a])};
_.g.zG=function(a){Lv(this.Pd,this.Me,a)};_.iw=function(a,b,c){a.Bk?c(a.Wn()):_.Yv(a,a.Ada,[b,c])};_.hw.prototype.Ada=function(a,b){this.Pd.Aj(this.Ob,this.Jj,a,this.Me,function(c,d){d?b({error:d}):b(c)},this.Da,this.c9,this.Bga)};_.hw.prototype.AW=_.jb(16);
var jw=function(a){var b=window.location;a=_.rc(a);a!==void 0&&b.assign(a)},kw=function(a){return Array.prototype.concat.apply([],arguments)},lw=function(){try{var a=Array.from((window.crypto||window.msCrypto).getRandomValues(new Uint8Array(64)))}catch(c){a=[];for(var b=0;b<64;b++)a[b]=Math.floor(Math.random()*256)}return _.Lh(a,3).substring(0,64)},mw=function(a){var b=[],c;for(c in a)if(a.hasOwnProperty(c)){var d=a[c];if(d===null||d===void 0)d="";b.push(encodeURIComponent(c)+"="+encodeURIComponent(d))}return b.join("&")},
nw=function(a,b){(b===void 0?0:b)||window.addEventListener("hashchange",function(){location.hash.includes("client_id")&&window.location.reload()});jw(a)},ow=function(a,b,c){if(!a.Pe)throw Error("na");b?_.Mv(a.Pd,a.Me,!0,void 0,c):_.Mv(a.Pd,a.Me,!0,a.Ge,c)},pw=function(a){if(!a.Pe)throw Error("na");return a.CI},qw,rw,sw,tw,uw,vw,ww,xw,yw,zw,Aw,Bw,Cw,Dw,Gw,Jw,Kw;
_.hw.prototype.AW=_.pb(16,function(a,b){var c=this.Pd,d=this.Ob,e=this.Me,f=_.bv(this.Jj);delete f.response_type;_.Jv(c,"getOnlineCode",{clientId:d,loginHint:a,request:f,sessionSelector:e},b)});_.fw.prototype.uZ=_.pb(15,function(a){pw(this)&&pw(this).access_token&&(this.Pd.revoke(this.Ob,pw(this).access_token,a),ow(this,!0))});
_.fw.prototype.wu=_.pb(14,function(){var a=this;return function(b){b&&b.authResult&&b.authResult.login_hint&&(a.WB?(b.authResult.client_id=a.Ob,nw(a.WB+"#"+mw(b.authResult))):a.pD(b.authResult.login_hint,a.Nh||b.authResult.login_hint!=a.Ge,!0,!0))}});
_.hw.prototype.wu=_.pb(13,function(a){var b=this;return function(c){c&&c.authResult&&c.authResult.login_hint?b.pu(function(d){_.Mv(b.Pd,b.Me,d&&d.disabled,c.authResult.login_hint,function(){_.iw(b,c.authResult.login_hint,a)})}):a(c&&c.authResult&&c.authResult.error?c.authResult:c&&c.authResult&&!c.authResult.login_hint?{error:"wrong_response_type"}:{error:"unknown_error"})}});_.$v.prototype.nX=_.pb(12,function(){this.Ob&&_.Jv(this.Pd,"startPolling",{clientId:this.Ob,origin:this.Cd,id:this.Hk})});
_.Gv.prototype.revoke=_.pb(8,function(a,b,c){_.Jv(this,"revoke",{clientId:a,token:b},c)});_.fw.prototype.revoke=_.pb(7,function(a){_.Yv(this,this.uZ,[a])});qw="openid email profile https://www.googleapis.com/auth/userinfo.email https://www.googleapis.com/auth/userinfo.profile https://www.googleapis.com/auth/plus.me https://www.googleapis.com/auth/plus.login".split(" ");
rw=function(){var a=navigator.userAgent,b;if(b=!!a&&a.indexOf("CriOS")!=-1)b=-1,(a=a.match(/CriOS\/(\d+)/))&&a[1]&&(b=parseInt(a[1],10)||-1),b=b<48;return b};
sw=function(){var a=navigator.userAgent.toLowerCase();if(!(a.indexOf("safari/")>-1&&a.indexOf("chrome/")<0&&a.indexOf("crios/")<0&&a.indexOf("android")<0))return!1;var b=RegExp("version/(\\d+)\\.(\\d+)[\\.0-9]*").exec(navigator.userAgent.toLowerCase());if(!b||b.length<3)return!1;a=parseInt(b[1],10);b=parseInt(b[2],10);return a>12||a==12&&b>=1};tw=function(a){return a.length>0&&a.every(function(b){return qw.includes(b)})};
uw=function(a,b,c,d,e,f,h){var k=_.mv(a,"authServerUrl");if(!k)throw Error("Y`"+a);a=_.bv(d);a.response_type=h||"permission";a.client_id=c;a.ss_domain=b;if(f&&f.extraQueryParams)for(var l in f.extraQueryParams)a[l]=f.extraQueryParams[l];(b=e)&&!(b=sw())&&(b=navigator.userAgent.toLowerCase(),b.indexOf("ipad;")>-1||b.indexOf("iphone;")>-1?(b=RegExp("os (\\d+)_\\d+(_\\d+)? like mac os x").exec(navigator.userAgent.toLowerCase()),b=!b||b.length<2?!1:parseInt(b[1],10)>=14):b=!1);b&&!a.prompt&&(a.prompt=
"select_account");return k+(k.indexOf("?")<0?"?":"&")+mw(a)};vw=function(a,b,c,d){if(!a.Ob)throw Error("oa");a.Hk=c||a.Y6||"auth"+Math.floor(Math.random()*1E6+1);b=b||{};b.extraQueryParams=b.extraQueryParams||{};if(!b.extraQueryParams.redirect_uri){var e=a.Cd.split("//");c=b.extraQueryParams;var f=e[0],h=e[1];e=a.Hk;var k=f.indexOf(":");k>0&&(f=f.substring(0,k));f=["storagerelay://",f,"/",h,"?"];f.push("id="+e);c.redirect_uri=f.join("")}return uw(a.Bb,a.Zd,a.Ob,a.qn,!0,b,d)};
ww=function(a,b,c){if(!a.Ob)throw Error("oa");return uw(a.Bb,a.Zd,a.Ob,a.qn,!1,b,c)};xw=function(a,b){a.Uo&&window.clearTimeout(a.Uo);a.Uo=window.setTimeout(function(){a.Hk==b&&(_.ov=void 0,a.Uo=null,a.Hk=void 0,a.Po({authResult:{error:"popup_closed_by_user"}}))},1E3)};
yw=function(a,b,c){if(!a.Ob)throw Error("pa");c=c||{};c=vw(a,c.sessionMeta,c.oneTimeId,c.responseType);(Object.hasOwnProperty.call(window,"ActiveXObject")&&!window.ActiveXObject||rw())&&_.Yv(a,a.nX,[]);var d=a.Hk;a.sY.open(c,b,function(){a.Hk==d&&xw(a,d)},function(){a.Hk=void 0;a.Po({authResult:{error:"popup_blocked_by_browser"}})})};
zw=function(a,b){var c=b||{};b=_.bv(a.qn);if(c.sessionMeta&&c.sessionMeta.extraQueryParams)for(var d in c.sessionMeta.extraQueryParams)b[d]=c.sessionMeta.extraQueryParams[d];var e;c.sessionMeta.extraQueryParams.scope&&(e=c.sessionMeta.extraQueryParams.scope.split(" "));!e&&b.scope&&(e=b.scope.split(" "));delete b.redirect_uri;delete b.origin;delete b.client_id;delete b.scope;b.prompt=="select_account"&&delete b.prompt;b.gsiwebsdk="fedcm";b.ss_domain=a.Zd;d=_.mv(a.Bb,"fedcmConfigUrl");c=c.responseType;
b.response_type=c;b.scope=e.join(" ");!b.nonce&&c.includes("id_token")&&(b.nonce="notprovided");c=navigator.userActivation.isActive?"active":"passive";e=tw(e)?["name","email","picture"]:[];return{identity:{providers:[{configURL:d,clientId:a.Ob,fields:e,params:b}],mode:c},mediation:"required"}};
Aw=function(a,b,c){if(!a.Ob)throw Error("pa");b=zw(a,b);navigator.credentials.get(b).then(function(d){d=JSON.parse(d.token);var e={client_id:d.client_id,login_hint:d.login_hint,expires_in:3600,scope:d.scope};d.code&&(e.code=d.code);d.id_token&&(e.id_token=d.id_token);a.Po({type:_.Ov.Qs,idpId:a.Bb,authResult:e})},function(d){d.message.indexOf("identity-credentials-get")>=0||d.message.indexOf("Content Security Policy")>=0?c():a.Po({type:_.Ov.Qs,idpId:a.Bb,authResult:{error:d}})})};
Bw=function(a,b,c){a.Es&&_.kv()?Aw(a,c,function(){return yw(a,b,c)}):yw(a,b,c)};Cw=function(a,b){b=b||{};var c=ww(a,b.sessionMeta,b.responseType);a.Es&&_.kv()&&a.q1?(a.WB=b.sessionMeta.extraQueryParams.redirect_uri,Aw(a,b,function(){return nw(c,!0)})):nw(c,!0)};Dw=function(a,b,c){a.Bk?c(a.Wn()):_.Yv(a,a.AW,[b,c])};_.Ew=function(a){_.ye(_.Je,"le",[]).push(a)};
_.Fw=function(a){for(var b=[],c=0,d=0;c<a.length;){var e=a[c++];if(e<128)b[d++]=String.fromCharCode(e);else if(e>191&&e<224){var f=a[c++];b[d++]=String.fromCharCode((e&31)<<6|f&63)}else if(e>239&&e<365){f=a[c++];var h=a[c++],k=a[c++];e=((e&7)<<18|(f&63)<<12|(h&63)<<6|k&63)-65536;b[d++]=String.fromCharCode(55296+(e>>10));b[d++]=String.fromCharCode(56320+(e&1023))}else f=a[c++],h=a[c++],b[d++]=String.fromCharCode((e&15)<<12|(f&63)<<6|h&63)}return b.join("")};
Gw=function(a){var b=[];_.Mh(a,function(c){b.push(c)});return b};_.Hw=function(a,b){_.ti[b||"token"]=a};_.Iw=function(a){delete _.ti[a||"token"]};Kw=function(){if(typeof MessageChannel!=="undefined"){var a=new MessageChannel,b={},c=b;a.port1.onmessage=function(){if(b.next!==void 0){b=b.next;var d=b.cb;b.cb=null;d()}};return function(d){c.next={cb:d};c=c.next;a.port2.postMessage(0)}}return function(d){_.Xa.setTimeout(d,0)}};_.gv={parse:function(a){a=_.Nf("["+String(a)+"]");if(a===!1||a.length!==1)throw new SyntaxError("JSON parsing failed.");return a[0]},stringify:function(a){return _.Of(a)}};_.hw.prototype.lG=function(a,b){_.Yv(this,this.N8,[a,b])};_.hw.prototype.N8=function(a,b){this.Pd.lG(this.Ob,a,this.Jj,this.Me,b)};_.Gv.prototype.lG=function(a,b,c,d,e){c=_.bv(c);_.Jv(this,"gsi:fetchLoginHint",{clientId:a,loginHint:b,request:c,sessionSelector:d},e)};var Lw,Mw=["client_id","cookie_policy","scope"],Nw="client_id cookie_policy fetch_basic_profile hosted_domain scope openid_realm disable_token_refresh login_hint ux_mode redirect_uri state prompt oidc_spec_compliant nonce enable_serial_consent enable_granular_consent include_granted_scopes response_type session_selection plugin_name ack_extension_date use_fedcm gsiwebsdk".split(" "),Ow=["authuser","after_redirect","access_type","hl"],Pw=["login_hint","prompt"],Qw={clientid:"client_id",cookiepolicy:"cookie_policy"},
Rw=["approval_prompt","authuser","login_hint","prompt","hd"],Sw=["login_hint","g-oauth-window","status"],Tw=Math.min(_.Ue("oauth-flow/authWindowWidth",599),screen.width-20),Uw=Math.min(_.Ue("oauth-flow/authWindowHeight",600),screen.height-30);var Vw=function(a){_.lb.call(this,a)};_.y(Vw,_.lb);Vw.prototype.name="gapi.auth2.ExternallyVisibleError";var Ww=function(){};Ww.prototype.select=function(a,b){if(a.sessions&&a.sessions.length==1&&(a=a.sessions[0],a.login_hint)){b(a);return}b()};var Xw=function(){};Xw.prototype.select=function(a,b){if(a.sessions&&a.sessions.length)for(var c=0;c<a.sessions.length;c++){var d=a.sessions[c];if(d.login_hint){b(d);return}}b()};var Yw=function(a){this.Z6=a};
Yw.prototype.select=function(a,b){if(a.sessions)for(var c=0;c<a.sessions.length;c++){var d=a.sessions[c];if(d.session_state&&d.session_state.extraQueryParams&&d.session_state.extraQueryParams.authuser==this.Z6){d.login_hint?b(d):b();return}}b()};var Zw=function(a){this.we=a;this.IC=[]};Zw.prototype.select=function(a){var b=0,c=this,d=function(e){if(e)a(e);else{var f=c.IC[b];f?(b++,c.we.dB(function(h){h?f.select(h,d):d()})):a()}};d()};var $w=function(a){a=new Zw(a);a.IC.push(new Ww);return a},ax=function(a){a=new Zw(a);a.IC.push(new Xw);return a},bx=function(a,b){b===void 0||b===null?b=$w(a):(a=new Zw(a),a.IC.push(new Yw(b)),b=a);return b};var cx=function(a){this.Jf=a;this.isActive=!0};cx.prototype.remove=function(){this.isActive=!1};cx.prototype.trigger=function(){};var dx=function(a){this.remove=function(){a.remove()};this.trigger=function(){a.trigger()}},ex=function(){this.qc=[]};ex.prototype.add=function(a){this.qc.push(a)};ex.prototype.notify=function(a){for(var b=this.qc,c=[],d=0;d<b.length;d++){var e=b[d];e.isActive&&(c.push(e),e=fx(e.Jf,a),e=(0,_.qk)(e),e=(0,_.ok)(e),Jw||(Jw=Kw()),Jw(e))}this.qc=c};var fx=function(a,b){return function(){a(b)}};var ox=function(a){this.La=null;this.sha=new nx(this);this.qc=new ex;a!=void 0&&this.set(a)};ox.prototype.set=function(a){a!=this.La&&(this.La=a,this.sha.value=a,this.qc.notify(this.La))};ox.prototype.get=function(){return this.La};ox.prototype.na=function(a){a=new px(this,a);this.qc.add(a);return a};ox.prototype.get=ox.prototype.get;var px=function(a,b){cx.call(this,b);this.mca=a};_.y(px,cx);px.prototype.trigger=function(){var a=this.Jf;a(this.mca.get())};
var nx=function(a){this.value=null;this.na=function(b){return new dx(a.na(b))}};var qx={Mja:"fetch_basic_profile",Nka:"login_hint",mma:"prompt",sma:"redirect_uri",Kma:"scope",goa:"ux_mode",wna:"state"},rx=function(a){this.Ka={};if(a&&!_.Eh(a))if(typeof a.get=="function")this.Ka=a.get();else for(var b in qx){var c=qx[b];c in a&&(this.Ka[c]=a[c])}};rx.prototype.get=function(){return this.Ka};rx.prototype.w_=function(a){this.Ka.scope=a;return this};rx.prototype.Fu=function(){return this.Ka.scope};
var sx=function(a,b){var c=a.Ka.scope;b=kw(b.split(" "),c?c.split(" "):[]);_.Dh(b);a.Ka.scope=b.join(" ")};_.g=rx.prototype;_.g.fga=function(a){this.Ka.prompt=a;return this};_.g.j$=function(){return this.Ka.prompt};_.g.Ifa=function(){_.Sf.warn("Property app_package_name no longer supported and was not set");return this};_.g.n9=function(){_.Sf.warn("Property app_package_name no longer supported")};_.g.wf=function(a){this.Ka.state=a};_.g.getState=function(){return this.Ka.state};var tx=function(){return["toolbar=no","location="+(window.opera?"no":"yes"),"directories=no,status=no,menubar=no,scrollbars=yes,resizable=yes,copyhistory=no","width="+Tw,"height="+Uw,"top="+(screen.height-Uw)/2,"left="+(screen.width-Tw)/2].join()},ux=function(a){a=a&&a.id_token;if(!a||!a.split(".")[1])return null;var b=(a.split(".")[1]+"...").replace(/^((....)+).?.?.?$/,"$1");a=JSON;var c=a.parse;b=Gw(b);return c.call(a,_.Fw(b))},vx=function(){Lw=_.Ue("auth2/idpValue","google");var a=_.Ue("oauth-flow/authUrl",
"https://accounts.google.com/o/oauth2/auth"),b=_.Ue("oauth-flow/idpIframeUrl","https://accounts.google.com/o/oauth2/iframe");a={fedcmConfigUrl:_.Ue("oauth-flow/fedcmConfigUrl","https://accounts.google.com/o/fedcm/config.json"),authServerUrl:a,idpIFrameUrl:b};_.nv(Lw,a)},wx=function(a,b,c){for(var d=0;d<b.length;d++){var e=b[d];if(d===b.length-1){a[e]=c;break}_.vb(a[e])||(a[e]={});a=a[e]}},xx=function(){var a=window.location.origin;a||(a=window.location.protocol+"//"+window.location.host);return a},
zx=function(){var a=yx();a.storage_path&&window.sessionStorage.setItem(a.storage_path,xx()+window.location.pathname);if(a.status.toLowerCase()=="enforced")throw new Vw("gapi.auth2 is disabled on this website, but it is still used on page "+window.location.href);a.status.toLowerCase()=="informational"&&_.Sf.warn("gapi.auth2 is disabled on this website, but it is still used on page "+window.location.href)},Ax=function(a){return _.ji.get("GSI_ALLOW_3PCD")==="1"?!0:a===!1?!1:a===!0||(_.Je.le||[]).includes("fedcm_migration_mod")?
!0:!1};var Bx=function(a){var b=a?(b=ux(a))?b.sub:null:null;this.Da=b;this.Ic=a?_.fk(a):null};_.g=Bx.prototype;_.g.getId=function(){return this.Da};_.g.IG=function(){var a=ux(this.Ic);return a?a.hd:null};_.g.yg=function(){return!!this.Ic};_.g.Rl=function(a){if(a)return this.Ic;a=Cx;var b=_.fk(this.Ic);!a.LA||a.HH||a.Maa||(delete b.access_token,delete b.scope);return b};_.g.mK=function(){return Cx.mK()};_.g.Xk=function(){this.Ic=null};_.g.N9=function(){return this.Ic?this.Ic.scope:null};
_.g.update=function(a){this.Da=a.Da;this.Ic=a.Ic;this.Ic.id_token?this.gy=new Dx(this.Ic):this.gy&&(this.gy=null)};var Ex=function(a){return a.Ic&&typeof a.Ic.session_state=="object"?_.fk(a.Ic.session_state.extraQueryParams||{}):{}};_.g=Bx.prototype;_.g.xG=function(){var a=Ex(this);return a&&a.authuser!==void 0&&a.authuser!==null?a.authuser:null};
_.g.Wk=function(a){var b=Cx,c=new rx(a);b.HH=c.Fu()?!0:!1;Cx.LA&&sx(c,"openid profile email");return new _.xk(function(d,e){var f=Ex(this);f.login_hint=this.getId();f.scope=c.Fu();Fx(b,d,e,f)},this)};_.g.Mu=function(a){return new _.xk(function(b,c){var d=a||{},e=Cx;d.login_hint=this.getId();e.Mu(d).then(b,c)},this)};_.g.z$=function(a){return this.Wk(a)};_.g.disconnect=function(){return Cx.disconnect()};_.g.o9=function(){return this.gy};
_.g.sA=function(a){if(!this.yg())return!1;var b=this.Ic&&this.Ic.scope?this.Ic.scope.split(" "):"";return _.Nb(a?a.split(" "):[],function(c){return _.tb(b,c)})};var Dx=function(a){a=ux(a);this.h9=a.sub;this.eh=a.name;this.v$=a.given_name;this.K8=a.family_name;this.fV=a.picture;this.Zy=a.email};_.g=Dx.prototype;_.g.getId=function(){return this.h9};_.g.mg=function(){return this.eh};_.g.L9=function(){return this.v$};_.g.H9=function(){return this.K8};_.g.T9=function(){return this.fV};_.g.Qn=function(){return this.Zy};var yx,Gx;yx=function(){var a=_.ji.get("G_AUTH2_MIGRATION");if(!a)return{status:"none"};a=/(enforced|informational)(?::(.*))?/i.exec(a);return a?{status:a[1].toLowerCase(),storage_path:a[2]}:(_.Sf.warn("The G_AUTH2_MIGRATION cookie value is not valid."),{status:"none"})};Gx=function(a){var b=location;if(a&&a!="none")return a=="single_host_origin"?b.protocol+"//"+b.host:a};
_.Hx=function(a){if(!a)throw new Vw("No cookiePolicy");var b=window.location.hostname;a=="single_host_origin"&&(a=window.location.protocol+"//"+b);if(a=="none")return null;var c=/^(https?:\/\/)([0-9.\-_A-Za-z]+)(?::(\d+))?$/.exec(a);if(!c)throw new Vw("Invalid cookiePolicy");a=c[2];c=c[1];var d={};d.dotValue=a.split(".").length;d.isSecure=c.indexOf("https")!=-1;d.domain=a;if(!_.Kj(b,"."+a)&&!_.Kj(b,a))throw new Vw("Invalid cookiePolicy domain");return d};var Jx=function(a){var b=a||{},c=Ix();_.Bb(Nw,function(d){typeof b[d]==="undefined"&&typeof c[d]!=="undefined"&&(b[d]=c[d])});return b},Ix=function(){for(var a={},b=document.getElementsByTagName("meta"),c=0;c<b.length;++c)if(b[c].name){var d=b[c].name;if(d.indexOf("google-signin-")==0){d=d.substring(14);var e=b[c].content;Qw[d]&&(d=Qw[d]);_.tb(Nw,d)&&e&&(a[d]=e=="true"?!0:e=="false"?!1:e)}}return a},Kx=function(a){return String(a).replace(/_([a-z])/g,function(b,c){return c.toUpperCase()})},Lx=function(a){_.Bb(Nw,
function(b){var c=Kx(b);typeof a[c]!=="undefined"&&typeof a[b]==="undefined"&&(a[b]=a[c],delete a[c])})},Mx=function(a){a=Jx(a);Lx(a);a.cookie_policy||(a.cookie_policy="single_host_origin");var b=Nw+Ow,c;for(c in a)b.indexOf(c)<0&&delete a[c];return a},Nx=function(a,b){if(!a)throw new Vw("Empty initial options.");for(var c=0;c<Mw.length;++c)if(!(b&&Mw[c]=="scope"||a[Mw[c]]))throw new Vw("Missing required parameter '"+Mw[c]+"'");_.Hx(a.cookie_policy)},Px=function(a){var b={authParameters:{redirect_uri:void 0,
response_type:"token id_token",scope:a.scope,"openid.realm":a.openid_realm,include_granted_scopes:!0},clientId:a.client_id,crossSubDomains:!0,domain:Gx(a.cookie_policy),disableTokenRefresh:!!a.disable_token_refresh,idpId:Lw};Ox(b,a);_.Bb(Pw,function(d){a[d]&&(b.authParameters[d]=a[d])});typeof a.enable_serial_consent=="boolean"&&(b.enableSerialConsent=a.enable_serial_consent);typeof a.enable_granular_consent=="boolean"&&(b.enableGranularConsent=a.enable_granular_consent);if(a.plugin_name)b.pluginName=
a.plugin_name;else{var c=_.Ue("auth2/pluginName");c&&(b.pluginName=c)}a.ack_extension_date&&(b.authParameters.ack_extension_date=a.ack_extension_date,b.ackExtensionDate=a.ack_extension_date);typeof a.use_fedcm==="boolean"&&(b.useFedCm=a.use_fedcm);return b},Ox=function(a,b){var c=b.oidc_spec_compliant;b=b.nonce;c&&(a.spec_compliant=c,b=b||lw());b&&(a.authParameters.nonce=b,a.forceTokenRefresh=!0,a.skipTokenCache=!0)},Ux=function(a){var b=a.client_id,c=a.cookie_policy,d=a.scope,e=a.openid_realm,f=
a.hosted_domain,h=a.oidc_spec_compliant,k=a.nonce,l=Qx(a),m={authParameters:{response_type:l,scope:d,"openid.realm":e},rpcAuthParameters:{response_type:l,scope:d,"openid.realm":e},clientId:b,crossSubDomains:!0,domain:Gx(c),idpId:Lw};f&&(m.authParameters.hd=f,m.rpcAuthParameters.hd=f);h&&(m.rpcAuthParameters.spec_compliant=h,k=k||lw());k&&(m.authParameters.nonce=k,m.rpcAuthParameters.nonce=k,m.forceTokenRefresh=!0,m.skipTokenCache=!0);_.Bb(Pw.concat(Ow),function(n){a[n]&&(m.authParameters[n]=a[n])});
a.authuser!==void 0&&a.authuser!==null&&(m.authParameters.authuser=a.authuser);typeof a.include_granted_scopes=="boolean"&&(b=new Rx(a.response_type||"token"),Sx(b)&&(m.authParameters.include_granted_scopes=a.include_granted_scopes),Tx(b)&&(m.rpcAuthParameters.include_granted_scopes=a.include_granted_scopes,a.include_granted_scopes===!1&&(m.forceTokenRefresh=!0,m.skipTokenCache=!0)));typeof a.enable_serial_consent=="boolean"&&(m.enableSerialConsent=a.enable_serial_consent);typeof a.enable_granular_consent==
"boolean"&&(m.enableGranularConsent=a.enable_granular_consent);a.plugin_name?m.pluginName=a.plugin_name:(b=_.Ue("auth2/pluginName"))&&(m.pluginName=b);a.ack_extension_date&&(m.authParameters.ack_extension_date=a.ack_extension_date,m.rpcAuthParameters.ack_extension_date=a.ack_extension_date,m.ackExtensionDate=a.ack_extension_date);typeof a.use_fedcm==="boolean"&&(m.useFedCm=a.use_fedcm);return m},Qx=function(a){a=new Rx(a.response_type||"token");var b=[];Tx(a)&&b.push("token");Vx(a,"id_token")&&b.push("id_token");
b.length==0&&(b=["token","id_token"]);return b.join(" ")},Wx=["permission","id_token"],Xx=/(^|[^_])token/,Rx=function(a){this.Or=[];this.cI(a)};Rx.prototype.cI=function(a){a?((a.indexOf("permission")>=0||a.match(Xx))&&this.Or.push("permission"),a.indexOf("id_token")>=0&&this.Or.push("id_token"),a.indexOf("code")>=0&&this.Or.push("code")):this.Or=Wx};var Sx=function(a){return Vx(a,"code")},Tx=function(a){return Vx(a,"permission")};Rx.prototype.toString=function(){return this.Or.join(" ")};
var Vx=function(a,b){var c=!1;_.Bb(a.Or,function(d){d==b&&(c=!0)});return c};var Zx=function(a,b,c){this.oJ=b;this.cda=a;for(var d in a)a.hasOwnProperty(d)&&Yx(this,d);if(c&&c.length)for(a=0;a<c.length;a++)this[c[a]]=this.oJ[c[a]]},Yx=function(a,b){a[b]=function(){return a.cda[b].apply(a.oJ,arguments)}};Zx.prototype.then=function(a,b,c){var d=this;return _.Bk().then(function(){return $x(d.oJ,a,b,c)})};_.mk(Zx);var Cx,ay,cy;Cx=null;_.by=function(){return Cx?ay():null};ay=function(){return new Zx(cy.prototype,Cx,["currentUser","isSignedIn"])};cy=function(a){delete a.include_granted_scopes;this.Ka=Px(a);this.U7=a.cookie_policy;this.Maa=!!a.scope;(this.LA=a.fetch_basic_profile!==!1)&&(this.Ka.authParameters.scope=dy(this,"openid profile email"));this.Ka.supportBlocked3PCookies=Ax(a.use_fedcm);this.Yu=a.hosted_domain;this.qha=a.ux_mode||"popup";this.WB=a.redirect_uri||null;this.ZH()};
cy.prototype.ZH=function(){this.currentUser=new ox(new Bx(null));this.isSignedIn=new ox(!1);this.we=new _.fw(this.Ka);this.QA=this.hr=null;this.Xba=new _.xk(function(a,b){this.hr=a;this.QA=b},this);this.zB={};this.qv=!0;ey(this);this.we.start()};
var ey=function(a){a.we.addEventListener("error",function(b){a.qv&&a.hr&&(a.qv=!1,a.QA({error:b.error,details:b.details}),a.hr=null,a.QA=null)});a.we.addEventListener("authResult",function(b){b&&b.authResult&&a.Cf(b);a.we.wu()(b)});a.we.addEventListener("tokenReady",function(b){var c=new Bx(b.response);if(a.Yu&&a.Yu!=c.IG())a.Cf({type:"tokenFailed",reason:"Account domain does not match hosted_domain specified by gapi.auth2.init.",accountDomain:c.IG(),expectedDomain:a.Yu});else{a.currentUser.get().update(c);
var d=a.currentUser;d.qc.notify(d.La);a.isSignedIn.set(!0);c=c.xG();(d=_.Hx(a.U7))&&c&&_.ji.set(["G_AUTHUSER_",window.location.protocol==="https:"&&d.hf?"S":"H",d.Ui].join(""),c,{domain:d.domain,secure:d.isSecure});_.Hw(b.response);a.Cf(b)}});a.we.addEventListener("noSessionBound",function(b){a.qv&&b.autoOpenAuthUrl?(a.qv=!1,$w(a.we).select(function(c){if(c&&c.login_hint){var d=a.we;_.Yv(d,d.pD,[c.login_hint,!0])}else a.currentUser.set(new Bx(null)),a.isSignedIn.set(!1),_.Iw(),a.Cf(b)})):(a.currentUser.set(new Bx(null)),
a.isSignedIn.set(!1),_.Iw(),a.Cf(b))});a.we.addEventListener("tokenFailed",function(b){a.Cf(b)});a.we.addEventListener("userLoggedOut",function(b){a.currentUser.get().Xk();var c=a.currentUser;c.qc.notify(c.La);a.isSignedIn.set(!1);_.Iw();a.Cf(b)})},$x=function(a,b,c,d){return a.Xba.then(function(e){if(b)return b(e.x$)},c,d)};cy.prototype.Cf=function(a){if(a){this.qv=!1;var b=a.type||"";if(this.zB[b])this.zB[b](a);this.hr&&(this.hr({x$:this}),this.QA=this.hr=null)}};
var fy=function(a,b){_.Wb(b,function(c,d){a.zB[d]=function(e){a.zB={};c(e)}})},Fx=function(a,b,c,d){d=_.fk(d);a.Yu&&(d.hd=a.Yu);var e=d.ux_mode||a.qha;delete d.ux_mode;delete d.app_package_name;var f={sessionMeta:{extraQueryParams:d},responseType:"permission id_token"};e=="redirect"?(d.redirect_uri||(d.redirect_uri=a.WB||xx()+window.location.pathname),gy(a,f)):(delete d.redirect_uri,hy(a,f),fy(a,{authResult:function(h){h.authResult&&h.authResult.error?c(h.authResult):fy(a,{tokenReady:function(){b(a.currentUser.get())},
tokenFailed:c})}}))};cy.prototype.Wk=function(a){return new _.xk(function(b,c){var d=new rx(a);this.HH=d.Fu()?!0:!1;this.LA?(d.Ka.fetch_basic_profile=!0,sx(d,"email profile openid")):d.Ka.fetch_basic_profile=!1;var e=dy(this,d.Fu());d.w_(e);Fx(this,b,c,d.get())},this)};
cy.prototype.Mu=function(a){var b=a||{};this.HH=!!b.scope;a=dy(this,b.scope);if(a=="")return _.Ck({error:"Missing required parameter: scope"});var c={scope:a,access_type:"offline",include_granted_scopes:!0};_.Bb(Rw,function(d){b[d]!=null&&(c[d]=b[d])});c.hasOwnProperty("prompt")||c.hasOwnProperty("approval_prompt")||(c.prompt="consent");b.redirect_uri=="postmessage"||b.redirect_uri==void 0?a=iy(this,c):(c.redirect_uri=b.redirect_uri,gy(this,{sessionMeta:{extraQueryParams:c},responseType:"code id_token"}),
a=_.Bk({message:"Redirecting to IDP."}));return a};
var iy=function(a,b){b.origin=xx();delete b.redirect_uri;hy(a,{sessionMeta:{extraQueryParams:b},responseType:"code permission id_token"});return new _.xk(function(c,d){fy(this,{authResult:function(e){(e=e&&e.authResult)&&e.code?c({code:e.code}):d(e&&e.error?e:{error:"unknown_error"})}})},a)},hy=function(a,b){wx(b,["sessionMeta","extraQueryParams","gsiwebsdk"],"2");Bw(a.we,tx(),b)},gy=function(a,b){wx(b,["sessionMeta","extraQueryParams","gsiwebsdk"],"2");Cw(a.we,b)};
cy.prototype.Xk=function(a){var b=a||!1;return new _.xk(function(c){ow(this.we,b,function(){c()})},this)};cy.prototype.BT=function(){return this.Ka.authParameters.scope};var dy=function(a,b){a=a.BT();b=kw(b?b.split(" "):[],a?a.split(" "):[]);_.Dh(b);return b.join(" ")};cy.prototype.mK=function(){var a=this;return new _.xk(function(b,c){fy(a,{noSessionBound:c,tokenFailed:c,userLoggedOut:c,tokenReady:function(d){b(d.response)}});a.we.PS()})};
cy.prototype.yP=function(a,b,c,d){if(a=typeof a==="string"?document.getElementById(a):a){var e=this;_.Bj(a,"click",function(){var f=b;typeof b=="function"&&(f=b());e.Wk(f).then(function(h){c&&c(h)},function(h){d&&d(h)})})}else d&&d({error:"Could not attach click handler to the element. Reason: element not found."})};cy.prototype.disconnect=function(){return new _.xk(function(a){this.we.revoke(function(){a()})},this)};cy.prototype.attachClickHandler=cy.prototype.yP;var jy;_.xk.prototype["catch"]=_.xk.prototype.vD;jy=null;_.ky=function(a){zx();a=Mx(a);if(Cx){if(_.Vu(a,jy||{}))return ay();throw new Vw("gapi.auth2 has been initialized with different options. Consider calling gapi.auth2.getAuthInstance() instead of gapi.auth2.init().");}Nx(a,a.fetch_basic_profile!==!1);vx();jy=a;Cx=new cy(a);_.Je.ga=1;return ay()};var my,oy,ly,qy,py,ry;
_.ny=function(a,b){zx();vx();a=Mx(a);Nx(a);var c=Ux(a);c.supportBlocked3PCookies=Ax(a.use_fedcm);var d=new _.hw(c);a.prompt=="none"?ly(d,a,function(e){e.status=e.error?{signed_in:!1,method:null,google_logged_in:!1}:{signed_in:!0,method:"AUTO",google_logged_in:!0};b(e)}):my(d,a,function(e){if(e.error)e.status={signed_in:!1,method:null,google_logged_in:!1};else{var f=e.access_token||e.id_token;e.status={signed_in:!!f,method:"PROMPT",google_logged_in:!!f}}e["g-oauth-window"]=d.sY.xi;b(e)})};
my=function(a,b,c){var d=new Rx(b.response_type);c=oy(a,d,c);var e={responseType:d.toString()};wx(e,["sessionMeta","extraQueryParams","gsiwebsdk"],b.gsiwebsdk||"2");Sx(d)&&wx(e,["sessionMeta","extraQueryParams","access_type"],b.access_type||"offline");b.redirect_uri&&wx(e,["sessionMeta","extraQueryParams","redirect_uri"],b.redirect_uri);b.state&&wx(e,["sessionMeta","extraQueryParams","state"],b.state);b=tx();a.Bk?c({authResult:{error:"idpiframe_initialization_failed",details:a.Wn().error}}):(a.rn=
c,Bw(a,b,e))};oy=function(a,b,c){if(Tx(b)){var d=py(c);return function(e){e&&e.authResult&&!e.authResult.error?a.wu(function(f){f&&!f.error?(f=_.fk(f),Sx(b)&&(f.code=e.authResult.code),d(f)):d(f?f:{error:"unknown_error"})})(e):d(e&&e.authResult?e.authResult:{error:"unknown_error"})}}return function(e){e&&e.authResult&&!e.authResult.error?c(_.fk(e.authResult)):c(e&&e.authResult?e.authResult:{error:"unknown_error"})}};
ly=function(a,b,c){if(Sx(new Rx(b.response_type))&&b.access_type=="offline")c({error:"immediate_failed",error_subtype:"access_denied"});else{var d=py(c);b.login_hint?a.lG(b.login_hint,function(e){e?qy(a,b,e,d):c({error:"immediate_failed",error_subtype:"access_denied"})}):b.authuser!==void 0&&b.authuser!==null?bx(a,b.authuser).select(function(e){e&&e.login_hint?qy(a,b,e.login_hint,d):d({error:"immediate_failed",error_subtype:"access_denied"})}):a.pu(function(e){e&&e.hint?qy(a,b,e.hint,d):e&&e.disabled?
d({error:"immediate_failed",error_subtype:"no_user_bound"}):(b.session_selection=="first_valid"?ax(a):$w(a)).select(function(f){f&&f.login_hint?qy(a,b,f.login_hint,d):d({error:"immediate_failed",error_subtype:"no_user_bound"})})})}};qy=function(a,b,c,d){b=new Rx(b.response_type);var e=0,f={},h=function(k){!k||k.error?d(k):(e--,_.gj(f,k),e==0&&d(f))};(Tx(b)||Vx(b,"id_token"))&&e++;Sx(b)&&e++;(Tx(b)||Vx(b,"id_token"))&&_.iw(a,c,h);Sx(b)&&Dw(a,c,h)};
py=function(a){return function(b){if(!b||b.error)_.Iw(),b?a(b):a({error:"unknown_error"});else{if(b.access_token){var c=_.fk(b);ry(c);delete c.id_token;delete c.code;_.Hw(c)}a(b)}}};ry=function(a){_.Bb(Sw,function(b){delete a[b]})};_.t("gapi.auth2.init",_.ky);_.t("gapi.auth2.authorize",function(a,b){if(Cx!=null)throw new Vw("gapi.auth2.authorize cannot be called after GoogleAuth has been initialized (i.e. with a call to gapi.auth2.init, or gapi.client.init when given a 'clientId' and a 'scope' parameters).");_.ny(a,function(c){ry(c);b(c)})});_.t("gapi.auth2._gt",function(){return _.ui()});_.t("gapi.auth2.enableDebugLogs",function(a){a=a!==!1;_.Wu=a!="0"&&!!a});_.t("gapi.auth2.getAuthInstance",_.by);
_.t("gapi.auth2.BasicProfile",Dx);_.t("gapi.auth2.BasicProfile.prototype.getId",Dx.prototype.getId);_.t("gapi.auth2.BasicProfile.prototype.getName",Dx.prototype.mg);_.t("gapi.auth2.BasicProfile.prototype.getGivenName",Dx.prototype.L9);_.t("gapi.auth2.BasicProfile.prototype.getFamilyName",Dx.prototype.H9);_.t("gapi.auth2.BasicProfile.prototype.getImageUrl",Dx.prototype.T9);_.t("gapi.auth2.BasicProfile.prototype.getEmail",Dx.prototype.Qn);_.t("gapi.auth2.GoogleAuth",cy);
_.t("gapi.auth2.GoogleAuth.prototype.attachClickHandler",cy.prototype.yP);_.t("gapi.auth2.GoogleAuth.prototype.disconnect",cy.prototype.disconnect);_.t("gapi.auth2.GoogleAuth.prototype.grantOfflineAccess",cy.prototype.Mu);_.t("gapi.auth2.GoogleAuth.prototype.signIn",cy.prototype.Wk);_.t("gapi.auth2.GoogleAuth.prototype.signOut",cy.prototype.Xk);_.t("gapi.auth2.GoogleAuth.prototype.getInitialScopes",cy.prototype.BT);_.t("gapi.auth2.GoogleUser",Bx);_.t("gapi.auth2.GoogleUser.prototype.grant",Bx.prototype.z$);
_.t("gapi.auth2.GoogleUser.prototype.getId",Bx.prototype.getId);_.t("gapi.auth2.GoogleUser.prototype.isSignedIn",Bx.prototype.yg);_.t("gapi.auth2.GoogleUser.prototype.getAuthResponse",Bx.prototype.Rl);_.t("gapi.auth2.GoogleUser.prototype.getBasicProfile",Bx.prototype.o9);_.t("gapi.auth2.GoogleUser.prototype.getGrantedScopes",Bx.prototype.N9);_.t("gapi.auth2.GoogleUser.prototype.getHostedDomain",Bx.prototype.IG);_.t("gapi.auth2.GoogleUser.prototype.grantOfflineAccess",Bx.prototype.Mu);
_.t("gapi.auth2.GoogleUser.prototype.hasGrantedScopes",Bx.prototype.sA);_.t("gapi.auth2.GoogleUser.prototype.reloadAuthResponse",Bx.prototype.mK);_.t("gapi.auth2.LiveValue",ox);_.t("gapi.auth2.LiveValue.prototype.listen",ox.prototype.na);_.t("gapi.auth2.LiveValue.prototype.get",ox.prototype.get);_.t("gapi.auth2.SigninOptionsBuilder",rx);_.t("gapi.auth2.SigninOptionsBuilder.prototype.getAppPackageName",rx.prototype.n9);_.t("gapi.auth2.SigninOptionsBuilder.prototype.setAppPackageName",rx.prototype.Ifa);
_.t("gapi.auth2.SigninOptionsBuilder.prototype.getScope",rx.prototype.Fu);_.t("gapi.auth2.SigninOptionsBuilder.prototype.setScope",rx.prototype.w_);_.t("gapi.auth2.SigninOptionsBuilder.prototype.getPrompt",rx.prototype.j$);_.t("gapi.auth2.SigninOptionsBuilder.prototype.setPrompt",rx.prototype.fga);_.t("gapi.auth2.SigninOptionsBuilder.prototype.get",rx.prototype.get);
_.Ye=_.Ye||{};
(function(){function a(b){var c="";if(b.nodeType==3||b.nodeType==4)c=b.nodeValue;else if(b.innerText)c=b.innerText;else if(b.innerHTML)c=b.innerHTML;else if(b.firstChild){c=[];for(b=b.firstChild;b;b=b.nextSibling)c.push(a(b));c=c.join("")}return c}_.Ye.createElement=function(b){if(!document.body||document.body.namespaceURI)try{var c=document.createElementNS("http://www.w3.org/1999/xhtml",b)}catch(d){}return c||document.createElement(b)};_.Ye.zQ=function(b){var c=_.Ye.createElement("iframe");try{var d=
["<","iframe"],e=b||{},f;for(f in e)e.hasOwnProperty(f)&&(d.push(" "),d.push(f),d.push('="'),d.push(_.Ye.WF(e[f])),d.push('"'));d.push("></");d.push("iframe");d.push(">");var h=_.Ye.createElement(d.join(""));h&&(!c||h.tagName==c.tagName&&h.namespaceURI==c.namespaceURI)&&(c=h)}catch(l){}d=c;b=b||{};for(var k in b)b.hasOwnProperty(k)&&(d[k]=b[k]);return c};_.Ye.dT=function(){if(document.body)return document.body;try{var b=document.getElementsByTagNameNS("http://www.w3.org/1999/xhtml","body");if(b&&
b.length==1)return b[0]}catch(c){}return document.documentElement||document};_.Ye.Eqa=function(b){return a(b)}})();
_.Dg=window.gapi&&window.gapi.util||{};
_.Dg=_.Dg={};_.Dg.getOrigin=function(a){return _.Fg(a)};
_.Ny=function(a){if(a.indexOf("GCSC")!==0)return null;var b={yj:!1};a=a.substr(4);if(!a)return b;var c=a.charAt(0);a=a.substr(1);var d=a.lastIndexOf("_");if(d==-1)return b;var e=_.Ly(a.substr(d+1));if(e==null)return b;a=a.substring(0,d);if(a.charAt(0)!=="_")return b;d=c==="E"&&e.hf;return!d&&(c!=="U"||e.hf)||d&&!_.My?b:{yj:!0,hf:d,D7:a.substr(1),domain:e.domain,Ui:e.Ui}};_.Oy=function(a,b){this.eh=a;a=b||{};this.Fca=Number(a.maxAge)||0;this.Zd=a.domain;this.Lm=a.path;this.pfa=!!a.secure};_.Oy.prototype.read=function(){for(var a=this.eh+"=",b=document.cookie.split(/;\s*/),c=0;c<b.length;++c){var d=b[c];if(d.indexOf(a)==0)return d.substr(a.length)}};
_.Oy.prototype.write=function(a,b){if(!Py.test(this.eh))throw"Invalid cookie name";if(!Qy.test(a))throw"Invalid cookie value";a=this.eh+"="+a;this.Zd&&(a+=";domain="+this.Zd);this.Lm&&(a+=";path="+this.Lm);b=typeof b==="number"?b:this.Fca;if(b>=0){var c=new Date;c.setSeconds(c.getSeconds()+b);a+=";expires="+c.toUTCString()}this.pfa&&(a+=";secure");document.cookie=a;return!0};_.Oy.prototype.clear=function(){this.write("",0)};var Qy=/^[-+/_=.:|%&a-zA-Z0-9@]*$/,Py=/^[A-Z_][A-Z0-9_]{0,63}$/;
_.Oy.iterate=function(a){for(var b=document.cookie.split(/;\s*/),c=0;c<b.length;++c){var d=b[c].split("="),e=d.shift();a(e,d.join("="))}};_.Ry=function(a){this.Nf=a};_.Ry.prototype.read=function(){if(Sy.hasOwnProperty(this.Nf))return Sy[this.Nf]};_.Ry.prototype.write=function(a){Sy[this.Nf]=a;return!0};_.Ry.prototype.clear=function(){delete Sy[this.Nf]};var Sy={};_.Ry.iterate=function(a){for(var b in Sy)Sy.hasOwnProperty(b)&&a(b,Sy[b])};var Ty=function(){this.La=null;this.key=function(){return null};this.getItem=function(){return this.La};this.setItem=function(a,b){this.La=b;this.length=1};this.removeItem=function(){this.clear()};this.clear=function(){this.La=null;this.length=0};this.length=0},Uy=function(a){try{var b=a||window.sessionStorage;if(!b)return!1;b.setItem("gapi.sessionStorageTest","gapi.sessionStorageTest"+b.length);b.removeItem("gapi.sessionStorageTest");return!0}catch(c){return!1}},Vy=function(a,b){this.eh=a;this.oN=
Uy(b)?b||window.sessionStorage:new Ty};Vy.prototype.read=function(){return this.oN.getItem(this.eh)};Vy.prototype.write=function(a){try{this.oN.setItem(this.eh,a)}catch(b){return!1}return!0};Vy.prototype.clear=function(){this.oN.removeItem(this.eh)};Vy.iterate=function(a){if(Uy())for(var b=window.sessionStorage.length,c=0;c<b;++c){var d=window.sessionStorage.key(c);a(d,window.sessionStorage[d])}};_.My=window.location.protocol==="https:";_.Wy=_.My||window.location.protocol==="http:"?_.Oy:_.Ry;_.Ly=function(a){var b=a.substr(1),c="",d=window.location.hostname;if(b!==""){c=parseInt(b,10);if(isNaN(c))return null;b=d.split(".");if(b.length<c-1)return null;b.length==c-1&&(d="."+d)}else d="";return{hf:a.charAt(0)=="S",domain:d,Ui:c}};var Xy,Yy,az,bz;Xy=_.ze();Yy=_.ze();_.Zy=_.ze();_.$y=_.ze();az="state code cookie_policy g_user_cookie_policy authuser prompt g-oauth-window status".split(" ");bz=function(a){this.mY=a;this.VI=null};
bz.prototype.write=function(a){var b=_.ze(),c=_.ze(),d=window.decodeURIComponent?decodeURIComponent:unescape,e;for(e in a)if(_.Ae(a,e)){var f=a[e];f=f.replace(/\+/g," ");c[e]=d(f);b[e]=a[e]}d=az.length;for(e=0;e<d;++e)delete c[az[e]];a=String(a.authuser||0);d=_.ze();d[a]=c;c=_.Of(d);this.mY.write(c);this.VI=b};bz.prototype.read=function(){return this.VI};bz.prototype.clear=function(){this.mY.clear();this.VI=_.ze()};_.cz=function(a){return a?{domain:a.domain,path:"/",secure:a.hf}:null};
Vy.iterate(function(a){var b=_.Ny(a);b&&b.yj&&(Xy[a]=new bz(new Vy(a)))});_.Wy.iterate(function(a){Xy[a]&&(Yy[a]=new _.Wy(a,_.cz(_.Ny(a))))});
_.ki=function(){function a(){e[0]=1732584193;e[1]=4023233417;e[2]=2562383102;e[3]=271733878;e[4]=3285377520;n=m=0}function b(p){for(var q=h,r=0;r<64;r+=4)q[r/4]=p[r]<<24|p[r+1]<<16|p[r+2]<<8|p[r+3];for(r=16;r<80;r++)p=q[r-3]^q[r-8]^q[r-14]^q[r-16],q[r]=(p<<1|p>>>31)&4294967295;p=e[0];var w=e[1],u=e[2],x=e[3],A=e[4];for(r=0;r<80;r++){if(r<40)if(r<20){var C=x^w&(u^x);var F=1518500249}else C=w^u^x,F=1859775393;else r<60?(C=w&u|x&(w|u),F=2400959708):(C=w^u^x,F=3395469782);C=((p<<5|p>>>27)&4294967295)+
C+A+F+q[r]&4294967295;A=x;x=u;u=(w<<30|w>>>2)&4294967295;w=p;p=C}e[0]=e[0]+p&4294967295;e[1]=e[1]+w&4294967295;e[2]=e[2]+u&4294967295;e[3]=e[3]+x&4294967295;e[4]=e[4]+A&4294967295}function c(p,q){if(typeof p==="string"){p=unescape(encodeURIComponent(p));for(var r=[],w=0,u=p.length;w<u;++w)r.push(p.charCodeAt(w));p=r}q||(q=p.length);r=0;if(m==0)for(;r+64<q;)b(p.slice(r,r+64)),r+=64,n+=64;for(;r<q;)if(f[m++]=p[r++],n++,m==64)for(m=0,b(f);r+64<q;)b(p.slice(r,r+64)),r+=64,n+=64}function d(){var p=[],
q=n*8;m<56?c(k,56-m):c(k,64-(m-56));for(var r=63;r>=56;r--)f[r]=q&255,q>>>=8;b(f);for(r=q=0;r<5;r++)for(var w=24;w>=0;w-=8)p[q++]=e[r]>>w&255;return p}for(var e=[],f=[],h=[],k=[128],l=1;l<64;++l)k[l]=0;var m,n;a();return{reset:a,update:c,digest:d,Ti:function(){for(var p=d(),q="",r=0;r<p.length;r++)q+="0123456789ABCDEF".charAt(Math.floor(p[r]/16))+"0123456789ABCDEF".charAt(p[r]%16);return q}}};var mi=function(a,b,c){var d=String(_.Xa.location.href);return d&&a&&b?[b,li(_.Fg(d),a,c||null)].join(" "):null},li=function(a,b,c){var d=[],e=[];if((Array.isArray(c)?2:1)==1)return e=[b,a],_.Bb(d,function(k){e.push(k)}),ni(e.join(" "));var f=[],h=[];_.Bb(c,function(k){h.push(k.key);f.push(k.value)});c=Math.floor((new Date).getTime()/1E3);e=f.length==0?[c,b,a]:[f.join(":"),c,b,a];_.Bb(d,function(k){e.push(k)});a=ni(e.join(" "));a=[c,a];h.length==0||a.push(h.join(""));return a.join("_")},ni=function(a){var b=
_.ki();b.update(a);return b.Ti().toLowerCase()};var pi;_.oi=function(){var a=_.Xa.__SAPISID||_.Xa.__APISID||_.Xa.__3PSAPISID||_.Xa.__1PSAPISID||_.Xa.__OVERRIDE_SID;if(a)return!0;typeof document!=="undefined"&&(a=new _.hi(document),a=a.get("SAPISID")||a.get("APISID")||a.get("__Secure-3PAPISID")||a.get("__Secure-1PAPISID"));return!!a};pi=function(a,b,c,d){(a=_.Xa[a])||typeof document==="undefined"||(a=(new _.hi(document)).get(b));return a?mi(a,c,d):null};
_.qi=function(a){var b=_.Fg(String(_.Xa.location.href)),c=[];if(_.oi()){b=b.indexOf("https:")==0||b.indexOf("chrome-extension:")==0||b.indexOf("chrome-untrusted://new-tab-page")==0||b.indexOf("moz-extension:")==0;var d=b?_.Xa.__SAPISID:_.Xa.__APISID;d||typeof document==="undefined"||(d=new _.hi(document),d=d.get(b?"SAPISID":"APISID")||d.get("__Secure-3PAPISID"));(d=d?mi(d,b?"SAPISIDHASH":"APISIDHASH",a):null)&&c.push(d);b&&((b=pi("__1PSAPISID","__Secure-1PAPISID","SAPISID1PHASH",a))&&c.push(b),(a=
pi("__3PSAPISID","__Secure-3PAPISID","SAPISID3PHASH",a))&&c.push(a))}return c.length==0?null:c.join(" ")};
_.ri=function(a){var b={SAPISIDHASH:!0,SAPISID3PHASH:!0,SAPISID1PHASH:!0,APISIDHASH:!0};return a&&(a.OriginToken||a.Authorization&&b[String(a.Authorization).split(" ")[0]])?!0:!1};_.si={DU:_.ri,Uba:_.oi,ET:function(){var a=null;_.oi()&&(a=window.__PVT,a==null&&(a=(new _.hi(document)).get("BEAT")));return a},ZS:_.qi};
var ts,us;_.ls=function(a){if(a instanceof _.ec)return a.GY;throw Error("j");};_.ms=function(a,b,c,d){this.top=a;this.right=b;this.bottom=c;this.left=d};_.ns=function(a,b){return a==b?!0:a&&b?a.x==b.x&&a.y==b.y:!1};_.os=function(a,b){this.x=a!==void 0?a:0;this.y=b!==void 0?b:0};_.g=_.os.prototype;_.g.clone=function(){return new _.os(this.x,this.y)};_.g.equals=function(a){return a instanceof _.os&&_.ns(this,a)};_.g.ceil=function(){this.x=Math.ceil(this.x);this.y=Math.ceil(this.y);return this};
_.g.floor=function(){this.x=Math.floor(this.x);this.y=Math.floor(this.y);return this};_.g.round=function(){this.x=Math.round(this.x);this.y=Math.round(this.y);return this};_.g.translate=function(a,b){a instanceof _.os?(this.x+=a.x,this.y+=a.y):(this.x+=Number(a),typeof b==="number"&&(this.y+=b));return this};_.g.scale=function(a,b){this.x*=a;this.y*=typeof b==="number"?b:a;return this};_.ps=function(a){return a.scrollingElement?a.scrollingElement:!_.zd&&_.ee(a)?a.documentElement:a.body||a.documentElement};
_.qs=function(a){var b=_.ps(a);a=a.defaultView;return new _.os(a.pageXOffset||b.scrollLeft,a.pageYOffset||b.scrollTop)};_.rs=function(a,b,c,d){return _.$d(a.Cc,b,c,d)};_.ss=function(a){return _.qs(a.Cc)};ts=function(a){return String(a).replace(/\-([a-z])/g,function(b,c){return c.toUpperCase()})};us=function(a){return a.replace(RegExp("(^|[\\s]+)([a-z])","g"),function(b,c,d){return c+d.toUpperCase()})};_.vs=function(a){return _.Zd(document,a)};_.g=_.ms.prototype;_.g.Qb=function(){return this.right-this.left};_.g.Nc=function(){return this.bottom-this.top};_.g.clone=function(){return new _.ms(this.top,this.right,this.bottom,this.left)};_.g.contains=function(a){return this&&a?a instanceof _.ms?a.left>=this.left&&a.right<=this.right&&a.top>=this.top&&a.bottom<=this.bottom:a.x>=this.left&&a.x<=this.right&&a.y>=this.top&&a.y<=this.bottom:!1};
_.g.expand=function(a,b,c,d){_.vb(a)?(this.top-=a.top,this.right+=a.right,this.bottom+=a.bottom,this.left-=a.left):(this.top-=a,this.right+=Number(b),this.bottom+=Number(c),this.left-=Number(d));return this};_.g.ceil=function(){this.top=Math.ceil(this.top);this.right=Math.ceil(this.right);this.bottom=Math.ceil(this.bottom);this.left=Math.ceil(this.left);return this};
_.g.floor=function(){this.top=Math.floor(this.top);this.right=Math.floor(this.right);this.bottom=Math.floor(this.bottom);this.left=Math.floor(this.left);return this};_.g.round=function(){this.top=Math.round(this.top);this.right=Math.round(this.right);this.bottom=Math.round(this.bottom);this.left=Math.round(this.left);return this};
_.g.translate=function(a,b){a instanceof _.os?(this.left+=a.x,this.right+=a.x,this.top+=a.y,this.bottom+=a.y):(this.left+=a,this.right+=a,typeof b==="number"&&(this.top+=b,this.bottom+=b));return this};_.g.scale=function(a,b){b=typeof b==="number"?b:a;this.left*=a;this.right*=a;this.top*=b;this.bottom*=b;return this};var ys,ws,Cs,Es;_.xs=function(a,b,c){if(typeof b==="string")(b=ws(a,b))&&(a.style[b]=c);else for(var d in b){c=a;var e=b[d],f=ws(c,d);f&&(c.style[f]=e)}};ys={};ws=function(a,b){var c=ys[b];if(!c){var d=ts(b);c=d;a.style[d]===void 0&&(d=(_.zd?"Webkit":_.yd?"Moz":null)+us(d),a.style[d]!==void 0&&(c=d));ys[b]=c}return c};_.zs=function(a,b){var c=a.style[ts(b)];return typeof c!=="undefined"?c:a.style[ws(a,b)]||""};
_.As=function(a,b){var c=_.Xd(a);return c.defaultView&&c.defaultView.getComputedStyle&&(a=c.defaultView.getComputedStyle(a,null))?a[b]||a.getPropertyValue(b)||"":""};_.Bs=function(a,b){return _.As(a,b)||(a.currentStyle?a.currentStyle[b]:null)||a.style&&a.style[b]};Cs=function(a){try{return a.getBoundingClientRect()}catch(b){return{left:0,top:0,right:0,bottom:0}}};
_.Fs=function(a,b){b=b||_.ps(document);var c=b||_.ps(document);var d=_.Ds(a),e=_.Ds(c),f=_.As(c,"borderLeftWidth");var h=_.As(c,"borderRightWidth");var k=_.As(c,"borderTopWidth"),l=_.As(c,"borderBottomWidth");h=new _.ms(parseFloat(k),parseFloat(h),parseFloat(l),parseFloat(f));c==_.ps(document)?(f=d.x-c.scrollLeft,d=d.y-c.scrollTop):(f=d.x-e.x-h.left,d=d.y-e.y-h.top);a=Es(a);e=c.clientHeight-a.height;h=c.scrollLeft;k=c.scrollTop;h+=Math.min(f,Math.max(f-(c.clientWidth-a.width),0));k+=Math.min(d,Math.max(d-
e,0));c=new _.os(h,k);b.scrollLeft=c.x;b.scrollTop=c.y};_.Ds=function(a){var b=_.Xd(a),c=new _.os(0,0);if(a==(b?_.Xd(b):document).documentElement)return c;a=Cs(a);b=_.ss(_.Yd(b));c.x=a.left+b.x;c.y=a.top+b.y;return c};_.Hs=function(a,b){var c=new _.os(0,0),d=_.fe(_.Xd(a));a:{try{_.Ub(d.parent);var e=!0;break a}catch(f){}e=!1}if(!e)return c;do e=d==b?_.Ds(a):_.Gs(a),c.x+=e.x,c.y+=e.y;while(d&&d!=b&&d!=d.parent&&(a=d.frameElement)&&(d=d.parent));return c};
_.Gs=function(a){a=Cs(a);return new _.os(a.left,a.top)};_.Js=function(a,b,c){if(b instanceof _.od)c=b.height,b=b.width;else if(c==void 0)throw Error("K");a.style.width=_.Is(b,!0);a.style.height=_.Is(c,!0)};_.Is=function(a,b){typeof a=="number"&&(a=(b?Math.round(a):a)+"px");return a};
_.Ks=function(a){var b=Es;if(_.Bs(a,"display")!="none")return b(a);var c=a.style,d=c.display,e=c.visibility,f=c.position;c.visibility="hidden";c.position="absolute";c.display="inline";a=b(a);c.display=d;c.position=f;c.visibility=e;return a};Es=function(a){var b=a.offsetWidth,c=a.offsetHeight,d=_.zd&&!b&&!c;return(b===void 0||d)&&a.getBoundingClientRect?(a=Cs(a),new _.od(a.right-a.left,a.bottom-a.top)):new _.od(b,c)};_.Ls=function(a,b){a.style.display=b?"":"none"};
_.Ns=function(a){var b=_.Yd(void 0),c=_.rs(b,"HEAD")[0];if(!c){var d=_.rs(b,"BODY")[0];c=b.wa("HEAD");d.parentNode.insertBefore(c,d)}d=b.wa("STYLE");var e;(e=_.Ec("style",document))&&d.setAttribute("nonce",e);_.Ms(d,a);b.appendChild(c,d)};_.Ms=function(a,b){b=_.ls(b);_.Xa.trustedTypes?_.se(a,b):a.innerHTML=b};_.Os=_.yd?"MozUserSelect":_.zd||_.wd?"WebkitUserSelect":null;
_.dz=function(a){_.bj.call(this);this.Nf=1;this.QB=[];this.VB=0;this.Tf=[];this.Sj={};this.T6=!!a};_.eb(_.dz,_.bj);_.g=_.dz.prototype;_.g.subscribe=function(a,b,c){var d=this.Sj[a];d||(d=this.Sj[a]=[]);var e=this.Nf;this.Tf[e]=a;this.Tf[e+1]=b;this.Tf[e+2]=c;this.Nf=e+3;d.push(e);return e};_.g.Nw=_.jb(18);_.g.unsubscribe=function(a,b,c){if(a=this.Sj[a]){var d=this.Tf;if(a=a.find(function(e){return d[e+1]==b&&d[e+2]==c}))return this.ll(a)}return!1};
_.g.ll=function(a){var b=this.Tf[a];if(b){var c=this.Sj[b];this.VB!=0?(this.QB.push(a),this.Tf[a+1]=function(){}):(c&&_.ej(c,a),delete this.Tf[a],delete this.Tf[a+1],delete this.Tf[a+2])}return!!b};
_.g.bp=function(a,b){var c=this.Sj[a];if(c){var d=Array(arguments.length-1),e=arguments.length,f;for(f=1;f<e;f++)d[f-1]=arguments[f];if(this.T6)for(f=0;f<c.length;f++)e=c[f],ez(this.Tf[e+1],this.Tf[e+2],d);else{this.VB++;try{for(f=0,e=c.length;f<e&&!this.isDisposed();f++){var h=c[f];this.Tf[h+1].apply(this.Tf[h+2],d)}}finally{if(this.VB--,this.QB.length>0&&this.VB==0)for(;c=this.QB.pop();)this.ll(c)}}return f!=0}return!1};var ez=function(a,b,c){_.vk(function(){a.apply(b,c)})};
_.dz.prototype.clear=function(a){if(a){var b=this.Sj[a];b&&(b.forEach(this.ll,this),delete this.Sj[a])}else this.Tf.length=0,this.Sj={}};_.dz.prototype.Yb=function(a){if(a){var b=this.Sj[a];return b?b.length:0}a=0;for(b in this.Sj)a+=this.Yb(b);return a};_.dz.prototype.ua=function(){_.dz.N.ua.call(this);this.clear();this.QB.length=0};
_.fz=function(a){this.Zga=a};_.gz=function(a){_.bj.call(this);this.qe=new _.dz(a);_.dj(this,this.qe)};_.fz.prototype.toString=function(){return this.Zga};_.eb(_.gz,_.bj);_.g=_.gz.prototype;_.g.subscribe=function(a,b,c){return this.qe.subscribe(a.toString(),b,c)};_.g.Nw=_.jb(17);_.g.unsubscribe=function(a,b,c){return this.qe.unsubscribe(a.toString(),b,c)};_.g.ll=function(a){return this.qe.ll(a)};_.g.bp=function(a,b){return this.qe.bp(a.toString(),b)};_.g.clear=function(a){this.qe.clear(a!==void 0?a.toString():void 0)};_.g.Yb=function(a){return this.qe.Yb(a!==void 0?a.toString():void 0)};
var hz,iz,lz,jz,mz,nz,kz;hz=function(a){var b=_.sc("");return _.cc(a.map(function(c){return _.dc(_.sc(c))}).join(_.dc(b).toString()))};iz=function(a){return hz(a)};lz=function(a){for(var b="",c=Object.keys(a),d=0;d<c.length;d++){var e=c[d],f=a[e];if(!jz.test(e))throw Error("j");if(f!==void 0&&f!==null){if(/^on./i.test(e))throw Error("j");kz.indexOf(e.toLowerCase())!==-1&&(f=_.kc(f)?f.toString():_.qc(String(f))||"about:invalid#zClosurez");f=e+'="'+_.sc(String(f))+'"';b+=" "+f}}return b};
_.oz=function(a,b){if(!jz.test("div"))throw Error("j");if(mz.indexOf("DIV")!==-1)throw Error("j");var c="<div";a&&(c+=lz(a));Array.isArray(b)||(b=b===void 0?[]:[b]);nz.indexOf("DIV")!==-1?c+=">":(a=iz(b.map(function(d){return d instanceof _.bc?d:_.sc(String(d))})),c+=">"+a.toString()+"</div>");return _.cc(c)};jz=/^[a-z][a-z\d-]*$/i;mz="APPLET BASE EMBED IFRAME LINK MATH META OBJECT SCRIPT STYLE SVG TEMPLATE".split(" ");nz="AREA BR COL COMMAND HR IMG INPUT KEYGEN PARAM SOURCE TRACK WBR".split(" ");
kz=["action","formaction","href"];_.pz=function(a,b){Array.isArray(b)||(b=[b]);b=b.map(function(c){return typeof c==="string"?c:c.Zo+" "+c.duration+"s "+c.timing+" "+c.delay+"s"});_.xs(a,"transition",b.join(","))};_.qz=function(a){var b=!1,c;return function(){b||(c=a(),b=!0);return c}}(function(){var a=_.je("DIV"),b=_.zd?"-webkit":_.yd?"-moz":null,c="transition:opacity 1s linear;";b&&(c+=b+"-transition:opacity 1s linear;");_.Fc(a,_.oz({style:c}));return _.zs(a.firstChild,"transition")!=""});
_.rz=function(a,b){_.Mj.call(this);this.Am=a||1;this.Xw=b||_.Xa;this.LP=(0,_.z)(this.Wga,this);this.rW=_.id()};_.eb(_.rz,_.Mj);_.g=_.rz.prototype;_.g.enabled=!1;_.g.Hc=null;_.g.setInterval=function(a){this.Am=a;this.Hc&&this.enabled?(this.stop(),this.start()):this.Hc&&this.stop()};
_.g.Wga=function(){if(this.enabled){var a=_.id()-this.rW;a>0&&a<this.Am*.8?this.Hc=this.Xw.setTimeout(this.LP,this.Am-a):(this.Hc&&(this.Xw.clearTimeout(this.Hc),this.Hc=null),this.dispatchEvent("tick"),this.enabled&&(this.stop(),this.start()))}};_.g.start=function(){this.enabled=!0;this.Hc||(this.Hc=this.Xw.setTimeout(this.LP,this.Am),this.rW=_.id())};_.g.stop=function(){this.enabled=!1;this.Hc&&(this.Xw.clearTimeout(this.Hc),this.Hc=null)};_.g.ua=function(){_.rz.N.ua.call(this);this.stop();delete this.Xw};
_.sz=function(a,b,c){if(typeof a==="function")c&&(a=(0,_.z)(a,c));else if(a&&typeof a.handleEvent=="function")a=(0,_.z)(a.handleEvent,a);else throw Error("xa");return Number(b)>2147483647?-1:_.Xa.setTimeout(a,b||0)};_.tz=function(a){_.Xa.clearTimeout(a)};
_.vz=function(){_.uz="oauth2relay"+String(2147483647*(0,_.Ng)()|0)};_.wz=new _.gz;_.xz=new _.fz("oauth");_.vz();_.Ue("oauth-flow/client_id");var yz=String(_.Ue("oauth-flow/redirectUri"));if(yz)yz.replace(/[#][\s\S]*/,"");else{var zz=_.Dg.getOrigin(window.location.href);_.Ue("oauth-flow/callbackUrl");encodeURIComponent(zz)}_.Dg.getOrigin(window.location.href);
var Bz,Cz,Dz,Ez,Fz,Gz,Hz,Iz,Jz,Kz,Lz,Sz,Tz,Uz,Vz,Wz,Xz,Yz,Zz,$z,aA,bA,cA,dA,eA,fA,gA,hA,iA,jA,kA,lA,mA,nA,oA,pA,qA,rA,sA,tA,uA,xA,wA,yA,zA,AA,BA,CA,DA,EA,FA,GA,IA;_.Az=function(a,b){if(_.Jh&&!b)return _.Xa.atob(a);var c="";_.Mh(a,function(d){c+=String.fromCharCode(d)});return c};Bz=function(a){var b=String(a("immediate")||"");a=String(a("prompt")||"");return b==="true"||a==="none"};Cz=function(a){return _.ci("enableMultilogin")&&a("cookie_policy")&&!Bz(a)?!0:!1};
Fz=function(){var a,b=null;_.Wy.iterate(function(c,d){c.indexOf("G_AUTHUSER_")===0&&(c=c.substring(11),c=_.Ly(c),!a||c.hf&&!a.hf||c.hf==a.hf&&c.Ui>a.Ui)&&(a=c,b=d)});return{f7:a,authuser:b}};Gz=[".APPS.GOOGLEUSERCONTENT.COM","@DEVELOPER.GSERVICEACCOUNT.COM"];Hz=function(a){a=a.toUpperCase();for(var b=Gz.length,c=0;c<b;++c){var d=a.split(Gz[c]);d.length==2&&d[1]===""&&(a=d[0])}a=a.replace(/-/g,"_").toUpperCase();a.length>40&&(b=new _.Mg,b.ox(a),a=b.Ti().toUpperCase());return a};
Iz=function(a){if(!a)return[];a=a.split("=");return a[1]?a[1].split("|"):[]};Jz=function(a){a=a.split(":");return{clientId:a[0].split("=")[1],Gfa:Iz(a[1]),ura:Iz(a[2]),nqa:Iz(a[3])}};Kz=function(a){var b=Fz(),c=b.f7;b=b.authuser;var d=a&&Hz(a);if(b!==null){var e;_.Wy.iterate(function(h,k){(h=_.Ny(h))&&h.yj&&(d&&h.D7!=d||h.hf==c.hf&&h.Ui==c.Ui&&(e=k))});if(e){var f=Jz(e);a=f&&f.Gfa[Number(b)];f=f&&f.clientId;if(a)return{authuser:b,qsa:a,clientId:f}}}return null};
Lz=function(a,b){a=_.ui(a);if(!a||!b&&a.error)return null;b=Math.floor((new Date).getTime()/1E3);return a.expires_at&&b>a.expires_at?null:a};_.Mz=function(a,b){if(b){var c=b;var d=a}else typeof a==="string"?d=a:c=a;c?_.Hw(c,d):_.Iw(d)};
Sz=function(a){if(!a)return null;a!=="single_host_origin"&&(a=_.Fg(a));var b=window.location.hostname,c=b,d=_.My;if(a!=="single_host_origin"){c=a.split("://");if(c.length==2)d=c.shift()==="https";else return _.Sf.log("WARNING invalid cookie_policy: "+a),null;c=c[0]}if(c.indexOf(":")!==-1)c=b="";else{a="."+c;if(b.lastIndexOf(a)!==b.length-a.length)return _.Sf.log("Invalid cookie_policy domain: "+c),null;c=a;b=c.split(".").length-1}return{domain:c,hf:d,Ui:b}};
Tz=function(a){var b=Sz(a);if(!b)return new _.Ry("G_USERSTATE_");a=["G_USERSTATE_",_.My&&b.hf?"S":"H",b.Ui].join("");var c=_.$y[a];c||(c={TI:63072E3},_.Be(_.cz(b),c),c=new _.Oy(a,c),_.$y[a]=c,b=c.read(),typeof b!=="undefined"&&b!==null&&(document.cookie=a+"=; expires=Thu, 01 Jan 1970 00:00:01 GMT; path=/",c.write(b)));return c};Uz=function(a){var b=Tz(a).read();a=_.ze();if(b){b=b.split(":");for(var c;c=b.shift();)c=c.split("="),a[c[0]]=c[1]}return a};
Vz=function(a,b,c){var d=Uz(b),e=d[a];d[a]="0";var f=[];_.Qm(d,function(k,l){f.push(l+"="+k)});var h=f.join(":");b=Tz(b);h?b.write(h):b.clear();d[a]!==e&&c&&c()};Wz=function(a,b){b=Uz(b);return b[a]=="0"||b[a]=="X"};Xz=function(a){a=Sz(a.g_user_cookie_policy);if(!a||a.hf&&!_.My)a=null;else{var b=["G_AUTHUSER_",_.My&&a.hf?"S":"H",a.Ui].join(""),c=_.Zy[b];c||(c=new _.Wy(b,_.cz(a)),_.Zy[b]=c);a=c}_.Ve("googleapis.config/sessionIndex",null);a.clear()};Yz=function(a){return Bz(function(b){return a[b]})};
Zz=0;$z=!1;aA=[];bA={};cA={};dA=null;eA=function(a){var b=_.uz;return function(c){if(this.f==b&&this.t==_.Xf.Pn(this.f)&&this.origin==_.Xf.eo(this.f))return a.apply(this,arguments)}};fA=function(a){if(a&&!decodeURIComponent(a).startsWith("m;/_/scs/"))throw Error("za");};gA=function(a){var b=_.Ye.Sg,c=b(a).jsh;if(c!=null)return fA(c),a;if(b=String(b().jsh||_.Je.h||""))fA(b),c=(a+"#").indexOf("#"),a=a.substr(0,c)+(a.substr(0,c).indexOf("?")!==-1?"&":"?")+"jsh="+encodeURIComponent(b)+a.substr(c);return a};
hA=function(){return!!_.Ue("oauth-flow/usegapi")};iA=function(a,b){hA()?dA.unregister(a):_.Xf.unregister(a+":"+b)};jA=function(a,b,c){hA()?dA.register(a,c,_.dn):_.Xf.register(a+":"+b,eA(c))};kA=function(){Dz.parentNode.removeChild(Dz)};
lA=function(a){var b=Dz;_.pz(b,[{Zo:"-webkit-transform",duration:1,timing:"ease",delay:0}]);_.pz(b,[{Zo:"transform",duration:1,timing:"ease",delay:0}]);_.sz(function(){b.style.webkitTransform="translate3d(0px,"+a+"px,0px)";b.style.transform="translate3d(0px,"+a+"px,0px)"},0)};mA=function(){var a=Ez+88;lA(a);Ez=a};nA=function(){var a=Ez-88;lA(a);Ez=a};
oA=function(a){var b=a?mA:nA,c=a?nA:mA;a=a?"-":"";Ez=parseInt(a+88,10);Dz.style.webkitTransform="translate3d(0px,"+a+88+"px,0px)";Dz.style.transform="translate3d(0px,"+a+88+"px,0px)";Dz.style.display="";Dz.style.visibility="visible";b();_.sz(c,4E3);_.sz(kA,5E3)};
pA=function(a){var b=_.Ue("oauth-flow/toast/position");b!=="top"&&(b="bottom");var c=document.createElement("div");Dz=c;c.style.cssText="position:fixed;left:0px;z-index:1000;width:100%;";_.xs(c,"visibility","hidden");_.xs(c,b,"-40px");_.xs(c,"height","128px");var d=c;if(_.Kr()){d=document.createElement("div");d.style.cssText="float:left;position:relative;left:50%;";c.appendChild(d);var e=document.createElement("div");e.style.cssText="float:left;position:relative;left:-50%";d.appendChild(e);d=e}e=
b=="top"?"-":"";Ez=parseInt(e+88,10);Dz.style.webkitTransform="translate3d(0px,"+e+88+"px,0px)";Dz.style.transform="translate3d(0px,"+e+88+"px,0px)";e=window;try{for(;e.parent!=e&&e.parent.document;)e=e.parent}catch(f){}e=e.document.body;try{e.insertBefore(c,e.firstChild)}catch(f){}_.an.openChild({url:":socialhost:/:session_prefix:_/widget/oauthflow/toast",queryParams:{clientId:a.client_id,idToken:a.id_token},where:d,onRestyle:function(){b==="top"?oA(!0):oA(!1)}})};
qA=function(a){var b=_.Po(),c=b&&b.scope;b=a&&a.scope;b=typeof b==="string"?b.split(" "):b||[];if(c){c=c.split(" ");for(var d=0;d<c.length;++d){var e=c[d];_.Om.call(b,e)==-1&&b.push(e)}b.length>0&&(a.scope=b.join(" "))}return a};
rA=function(a,b){var c=null;a&&b&&(c=b.client_id=b.client_id||a.client_id,b.scope=b.scope||a.scope,b.g_user_cookie_policy=a.cookie_policy,b.cookie_policy=b.cookie_policy||a.cookie_policy,b.response_type=b.response_type||a.response_type);if(b){b.issued_at||(b.issued_at=String(Math.floor((new Date).getTime()/1E3)));var d=parseInt(b.expires_in,10)||86400;b.error&&(d=_.Ue("oauth-flow/errorMaxAge")||86400);b.expires_in=String(d);b.expires_at||(b.expires_at=String(Math.floor((new Date).getTime()/1E3)+d));
b._aa||b.error||Kz(c)!=null||!Yz(a)||(b._aa="1");a=b.status={};a.google_logged_in=!!b.session_state;c=a.signed_in=!!b.access_token;a.method=c?b["g-oauth-window"]?"PROMPT":"AUTO":null}return b};sA=function(a){a=a&&a.id_token;if(!a||!a.split(".")[1])return null;a=(a.split(".")[1]+"...").replace(/^((....)+)\.?\.?\.?$/,"$1");a=_.Nf(_.Az(a,!0));if(a===!1)throw Error("Aa");return a};tA=function(a){return(a=sA(a))?a.sub:null};
uA=function(a){a&&aA.push(a);a=_.uz;var b=document.getElementById(a),c=(new Date).getTime();if(b){if(Zz&&c-Zz<6E4)return;var d=_.Xf.Pn(a);d&&(iA("oauth2relayReady",d),iA("oauth2callback",d));b.parentNode.removeChild(b);if(/Firefox/.test(navigator.userAgent))try{window.frames[a]=void 0}catch(f){}_.vz();a=_.uz}Zz=c;var e=String(2147483647*(0,_.Ng)()|0);b=_.Ue("oauth-flow/proxyUrl")||_.Ue("oauth-flow/relayUrl");hA()?dA=_.an.openChild({where:_.Ye.dT(),url:b,id:a,attributes:{style:{width:"1px",height:"1px",
position:"absolute",top:"-100px",display:"none"},"aria-hidden":"true"},dontclear:!0}):(b=[b,"?parent=",encodeURIComponent(_.Dg.getOrigin(window.location.href)),"#rpctoken=",e,"&forcesecure=1"].join(""),c=_.Ye.dT(),d=_.Ye.zQ({name:a,id:a}),d.src=gA(b),d.style.width="1px",d.style.height="1px",d.style.position="absolute",d.style.top="-100px",d.tabIndex=-1,d.setAttribute("aria-hidden","true"),c.appendChild(d),_.Xf.Dw(a));jA("oauth2relayReady",e,function(){iA("oauth2relayReady",e);var f=aA;if(f!==null){aA=
null;for(var h=f.length,k=0;k<h;++k)f[k]()}});jA("oauth2callback",e,function(f){var h=_.Ye.Sg;h=h(f);var k=h.state;f=k;f=f.replace(/\|.*$/,"");f={}.hasOwnProperty.call(cA,f)?cA[f]:null;h.state=f;if(h.state!=null){f=bA[k];delete bA[k];k=f&&f.key||"token";var l=h=rA(f&&f.params,h);var m=(m=tA(l))?Wz(m,l.cookie_policy):!1;!m&&l&&(" "+(l.scope||"")+" ").indexOf(" https://www.googleapis.com/auth/plus.login ")>=0&&_.Ue("isLoggedIn")&&(l&&l._aa)==="1"&&(l._aa="0",$z||($z=!0,pA(l)));_.Mz(k,h);h=Lz(k);if(f){k=
f.popup;l=f.after_redirect;if(k&&"keep_open"!=l)try{k.close()}catch(n){}f.callback&&(f.callback(h),f.callback=null)}}})};_.vA=function(a){aA!==null?uA(a):a&&a()};xA=function(a,b){var c=wA,d=tA(a);d&&(Xz(a),Vz(d,b,function(){if(c){var e={error:"user_signed_out"};e.client_id=a.client_id;e.g_user_cookie_policy=a.g_user_cookie_policy;e.scope=a.scope;e.response_type=a.response_type;e.session_state=a.session_state;e=rA(null,e);c(e)}}))};
wA=function(a){a||(a=Lz(void 0,!0));a&&typeof a==="object"||(a={error:"invalid_request",error_description:"no callback data"});var b=a.error_description;b&&window.console&&(window.console.error(a.error),window.console.error(b));a.error||(_.Je.drw=null);_.Mz(a);if(b=a.authuser)_.Ue("googleapis.config/sessionIndex"),_.Ve("googleapis.config/sessionIndex",b);_.wz.bp(_.xz,a);return a};yA=["client_id","cookie_policy","response_type"];zA="client_id response_type login_hint authuser prompt include_granted_scopes after_redirect access_type hl state".split(" ");
AA=function(a){var b=_.fk(a);b.session_state&&b.session_state.extraQueryParams&&(b.authuser=b.session_state.extraQueryParams.authuser);b.session_state=null;a.expires_at&&(b.expires_at=parseInt(a.expires_at/1E3).toString());a.expires_in&&(b.expires_in=a.expires_in.toString());a.first_issued_at&&(b.issued_at=parseInt(a.first_issued_at/1E3).toString(),delete b.first_issued_at);_.Hw(b);return b};
BA=function(a){if(a.include_granted_scopes===void 0){var b=_.Ue("include_granted_scopes");a.include_granted_scopes=!!b}};CA=function(a){window.console&&(typeof window.console.warn==="function"?window.console.warn(a):typeof window.console.log==="function"&&window.console.log(a))};
DA=function(a){var b=a||{},c={};_.Bb(zA,function(d){b[d]!=null&&(c[d]=b[d])});a=_.Ue("googleapis/overrideClientId");a!=null&&(c.client_id=a);BA(c);typeof b.scope==="string"?c.scope=b.scope:Array.isArray(b.scope)&&(c.scope=b.scope.join(" "));b["openid.realm"]!=null&&(c.openid_realm=b["openid.realm"]);b.cookie_policy!=null?c.cookie_policy=b.cookie_policy:b.cookiepolicy!=null&&(c.cookie_policy=b.cookiepolicy);c.login_hint==null&&b.user_id!=null&&(c.login_hint=b.user_id);try{_.Hx(c.cookie_policy)}catch(d){c.cookie_policy&&
CA("The cookie_policy configuration: '"+c.cookie_policy+"' is illegal, and thus ignored."),delete c.cookie_policy}b.hd!=null&&(c.hosted_domain=b.hd);c.prompt==null&&(b.immediate==1||b.immediate=="true"?c.prompt="none":b.approval_prompt=="force"&&(c.prompt="consent"));c.prompt=="none"&&(c.session_selection="first_valid");c.prompt=="none"&&c.access_type=="offline"&&delete c.access_type;typeof c.authuser==="undefined"&&(a=_.gi(),a!=null&&(c.authuser=a));a=b.redirect_uri||_.Ue("oauth-flow/redirectUri");
a!=null&&a!="postmessage"&&(c.redirect_uri=a);c.gsiwebsdk="shim";return c};
EA=function(a,b){var c=DA(a),d=new _.xk(function(e,f){_.ny(c,function(h){var k=h||{};_.Bb(yA,function(l){k[l]==null&&(k[l]=c[l])});!c.include_granted_scopes&&a&&a.scope&&(k.scope=a.scope);a&&a.state!=null&&(k.state=a.state);k.error?(c.prompt=="none"&&k.error=="user_logged_out"&&(k.error="immediate_failed_user_logged_out"),f(k)):(h=AA(k),h.authuser!=null&&_.Ve("googleapis.config/sessionIndex",h.authuser),e(h))})});b&&d.then(b,b);return d};FA=_.si.ZS;GA=null;
_.JA=function(a,b){if(a.approvalprompt!=="force"){a=_.HA(a);a.prompt="none";delete a.redirect_uri;delete a.approval_prompt;delete a.immediate;if(b=!b)GA?(a.client_id!==GA.client_id&&window.console&&window.console.log&&window.console.log("Ignoring mismatched page-level auth param client_id="+a.client_id),b=!0):(GA=a,b=!1);b||IA(a)}};
_.HA=function(a){var b=a.redirecturi||"postmessage",c=_.yc((a.scope||"").replace(/[\s\xa0]+/g," "));b={client_id:a.clientid,redirect_uri:b,response_type:"code token id_token gsession",scope:c};a.approvalprompt&&(b.approval_prompt=a.approvalprompt);a.state&&(b.state=a.state);a.openidrealm&&(b["openid.realm"]=a.openidrealm);c=a.accesstype=="offline"?!0:(c=a.redirecturi)&&c!="postmessage";c&&(b.access_type="offline");a.requestvisibleactions&&(b.request_visible_actions=_.yc(a.requestvisibleactions.replace(/[\s\xa0]+/g,
" ")));a.after_redirect&&(b.after_redirect=a.after_redirect);a.cookiepolicy&&a.cookiepolicy!=="none"&&(b.cookie_policy=a.cookiepolicy);typeof a.includegrantedscopes!="undefined"&&(b.include_granted_scopes=a.includegrantedscopes);a.e&&(b.e=a.e);(a=a.authuser||_.Ue("googleapis.config/sessionIndex"))&&(b.authuser=a);(a=_.Ue("useoriginassocialhost"))&&(b.use_origin_as_socialhost=a);return b};IA=function(a){_.Bp("waaf0","signin","0");EA(a,function(b){_.Bp("waaf1","signin","0");wA(b)})};
_.KA=function(a){a=_.HA(a);_.Ve("oauth-flow/authWindowWidth",445);_.Ve("oauth-flow/authWindowHeight",615);IA(a)};_.LA=function(a){_.wz.unsubscribe(_.xz,a);_.wz.subscribe(_.xz,a)};var SA,VA;_.NA=function(a){return a.cookiepolicy?!0:(_.MA("cookiepolicy is a required field.  See https://developers.google.com/+/web/signin/#button_attr_cookiepolicy for more information."),!1)};_.MA=function(a){window.console&&(window.console.error?window.console.error(a):window.console.log&&window.console.log(a))};_.RA=function(a,b){var c=_.Po();_.Be(a,c);c=qA(c);if(_.NA(c)){var d=_.OA();_.PA(c);b?_.Ie(b,"click",function(){_.QA(c,d)}):_.QA(c,d)}};
_.OA=function(){var a=new SA;_.LA(function(b){a.UI&&b&&(b.access_token&&_.Ve("isPlusUser",!0),b["g-oauth-window"]&&(a.UI=!1,_.Sf.warn("OTA app install is no longer supported.")))});return a};SA=function(){this.UI=!1};_.PA=function(a){a=_.TA(a);_.UA(a.callback);_.vA(function(){_.JA(a)})};_.TA=function(a){VA(a);a.redirecturi&&delete a.redirecturi;Cz(function(b){return a[b]})||(a.authuser=0);return a};VA=function(a){/^\s*$/.test(a.scope||"")&&(a.scope="https://www.googleapis.com/auth/plus.login")};
_.UA=function(a){if(typeof a==="string")if(window[a])a=window[a];else{_.MA('Callback function named "'+a+'" not found');return}a&&_.LA(a)};_.QA=function(a,b){b.UI=!0;a=_.TA(a);_.KA(a)};_.t("gapi.auth.authorize",EA);_.t("gapi.auth.checkSessionState",function(a,b){var c=_.ze();c.client_id=a.client_id;c.session_state=a.session_state;_.vA(function(){hA()?dA.send("check_session_state",c,function(d){b.call(null,d[0])},_.dn):_.Xf.call(_.uz,"check_session_state",eA(function(d){b.call(null,d)}),c.session_state,c.client_id)})});_.t("gapi.auth.getAuthHeaderValueForFirstParty",FA);_.t("gapi.auth.getToken",Lz);
_.t("gapi.auth.getVersionInfo",function(a,b){_.vA(function(){var c=_.qi()||"",d=null,e=null;c&&(e=c.split(" "),e.length==2&&(d=e[1]));d?hA()?dA.send("get_versioninfo",{xapisidHash:d,sessionIndex:b},function(f){a(f[0])},_.dn):_.Xf.call(_.uz,"get_versioninfo",eA(function(f){a(f)}),d,b):a()})});_.t("gapi.auth.init",_.vA);_.t("gapi.auth.setToken",_.Mz);_.t("gapi.auth.signIn",function(a){_.RA(a)});_.t("gapi.auth.signOut",function(){var a=Lz();a&&xA(a,a.cookie_policy)});
_.t("gapi.auth.unsafeUnpackIdToken",sA);_.t("gapi.auth._pimf",_.JA);_.t("gapi.auth._oart",pA);_.t("gapi.auth._guss",function(a){return Tz(a).read()});
var WA=_.Po();WA.clientid&&WA.scope&&WA.callback&&!_.Ue("disableRealtimeCallback")&&_.PA(WA);
var sy=function(){};var uy;uy=function(){};_.eb(uy,sy);uy.prototype.Ct=function(){return new XMLHttpRequest};_.ty=new uy;
_.Nh=function(a){var b=[],c=0,d;for(d in a)b[c++]=d;return b};_.Oh=function(a){return a==null?"":String(a)};_.Ph=function(a,b,c,d,e,f,h){var k="";a&&(k+=a+":");c&&(k+="//",b&&(k+=b+"@"),k+=c,d&&(k+=":"+d));e&&(k+=e);f&&(k+="?"+f);h&&(k+="#"+h);return k};_.Qh=RegExp("^(?:([^:/?#.]+):)?(?://(?:([^\\\\/?#]*)@)?([^\\\\/?#]*?)(?::([0-9]+))?(?=[\\\\/?#]|$))?([^?#]+)?(?:\\?([^#]*))?(?:#([\\s\\S]*))?$");
_.Rh=function(a,b){if(!b)return a;var c=a.indexOf("#");c<0&&(c=a.length);var d=a.indexOf("?");if(d<0||d>c){d=c;var e=""}else e=a.substring(d+1,c);a=[a.slice(0,d),e,a.slice(c)];c=a[1];a[1]=b?c?c+"&"+b:b:c;return a[0]+(a[1]?"?"+a[1]:"")+a[2]};_.Sh=function(a,b,c){if(Array.isArray(b))for(var d=0;d<b.length;d++)_.Sh(a,String(b[d]),c);else b!=null&&c.push(a+(b===""?"":"="+encodeURIComponent(String(b))))};_.Th=function(a){var b=[],c;for(c in a)_.Sh(c,a[c],b);return b.join("&")};
_.Uh=function(a,b){b=_.Th(b);return _.Rh(a,b)};
var Ti=function(a,b){a=_.Ye.zQ({id:a,name:a});a.style.width="1px";a.style.height="1px";a.style.position="absolute";a.style.top="-100px";a.style.display="none";if(window.navigator){var c=window.navigator.userAgent||"";var d=window.navigator.product||"";c=c.indexOf("Opera")!=0&&c.indexOf("WebKit")==-1&&d=="Gecko"&&c.indexOf("rv:1.")>0}else c=!1;a.src=c?"about:blank":b;a.tabIndex=-1;typeof a.setAttribute==="function"?a.setAttribute("aria-hidden","true"):a["aria-hidden"]="true";document.body.appendChild(a);
c&&(a.src=b);return a};_.si={DU:_.ri,Uba:_.oi,ET:function(){var a=null;_.oi()&&(a=window.__PVT,a==null&&(a=(new _.hi(document)).get("BEAT")));return a},ZS:_.qi};var Vi,Ui;Vi=function(){return!!Ui("auth/useFirstPartyAuthV2")};Ui=function(a){return _.Ue("googleapis.config/"+a)};
_.Wi=function(a,b,c){a=a===void 0?{}:a;b=b===void 0?window.location.href:b;c=c===void 0?"auto":c;if(c=="none")return a;var d=a.Authorization,e=a.OriginToken;if(!d&&!e){(e=_.ui())&&e.access_token&&(c=="oauth2"||c=="auto")&&(d=String(e.token_type||"Bearer")+" "+e.access_token);if(e=!d)e=(!!Ui("auth/useFirstPartyAuth")||c=="1p")&&c!="oauth2";if(e&&_.oi()){if(Vi()){d=Ui("primaryEmail");c=Ui("appDomain");e=Ui("fogId");var f=[];d&&f.push({key:"e",value:d});c&&f.push({key:"a",value:c});e&&f.push({key:"u",
value:e});d=_.qi(f)}else d=_.qi();d&&(c=a["X-Goog-AuthUser"],b=_.gi(b),b=c||b,_.wc(_.Oh(b))&&(!Vi()||Vi()&&_.wc(_.Oh(Ui("primaryEmail")))&&_.wc(_.Oh(Ui("appDomain")))&&_.wc(_.Oh(Ui("fogId"))))&&(b="0"),_.wc(_.Oh(b))||(a["X-Goog-AuthUser"]=b))}d?a.Authorization=d:Ui("auth/useOriginToken")!==!1&&(e=_.si.ET())&&(a.OriginToken=e)}return a};_.Xi=function(){function a(n,p,q,r,w){var u=f("proxy");if(r||!u){u=f("root");var x=f("root-1p")||u;u=u||"https://content.googleapis.com";x=x||"https://clients6.google.com";var A=f("xd3")||"/static/proxy.html";u=(r||String(p?x:u))+A}u=String(u);q&&(u+=(u.indexOf("?")>=0?"&":"?")+"usegapi=1");(p=_.Ye.Sg().jsh||_.Je.h)&&(u+=(u.indexOf("?")>=0?"&":"?")+"jsh="+encodeURIComponent(p));u+="#parent="+encodeURIComponent(w!=null?String(w):_.Dg.getOrigin(document.location.href));return u+("&rpctoken="+n)}function b(n,
p,q,r,w){var u=d(q,r,w);k[u]||(q=Ti(u,p),_.Xf.register("ready:"+n,function(){_.Xf.unregister("ready:"+n);if(!l[u]){l[u]=!0;var x=m[u];m[u]=[];for(var A=0,C=x.length;A<C;++A){var F=x[A];e(F.kp,F.Fea,F.callback)}}}),_.Xf.Dw(u,p),k[u]=q)}function c(n,p,q){var r=String(2147483647*_.Pi()|0),w=a(r,n,p,q);_.Uf(function(){b(r,w,n,p,q)})}function d(n,p,q){n=a("",n,p,q,"");q=h[n+p];if(!q){q=new _.Mg;q.ox(n);q=q.Ti().toLowerCase();var r=_.Pi();q+=r;h[n+p]=q}return"apiproxy"+q}function e(n,p,q){var r=void 0,
w=!1;if(n!=="makeHttpRequests")throw'only "makeHttpRequests" RPCs are implemented';var u=function(O){if(O){if(typeof r!="undefined"&&typeof O.root!="undefined"&&r!=O.root)throw"all requests in a batch must have the same root URL";r=O.root||r;w=_.si.DU(O.headers)}};if(p)for(var x=0,A=p.length;x<A;++x){var C=p[x];C&&u(C.params)}u=!!f("useGapiForXd3");var F=d(w,u,r);k[F]||c(w,u,r);l[F]?_.Xf.call(F,n,function(O){if(this.f==F&&this.t==_.Xf.Pn(this.f)&&this.origin==_.Xf.eo(this.f)){var E=_.Nf(O);q(E,O)}},
p):(m[F]||(m[F]=[]),m[F].push({kp:n,Fea:p,callback:q}))}function f(n){return _.Ue("googleapis.config/"+n)}var h={},k={},l={},m={};return{Bpa:function(n,p,q){return _.Wi(n,p,q)},Wm:e}}();
var Ug={Rha:"Authorization",A2:"Content-ID",qia:"Content-Transfer-Encoding",ria:"Content-Type",Yia:"Date",Pla:"OriginToken",mka:"hotrod-board-name",nka:"hotrod-chrome-cpu-model",oka:"hotrod-chrome-processors",Aoa:"WWW-Authenticate",Coa:"X-Ad-Manager-Impersonation",Boa:"X-Ad-Manager-Debug-Info",Eoa:"X-ClientDetails",Foa:"X-Cloudaicompanion-Trace-Id",Goa:"X-Compass-Routing-Destination",Joa:"X-Goog-AuthUser",Ooa:"X-Goog-Encode-Response-If-Executable",Hoa:"X-Google-Consent",Ioa:"X-Google-EOM",Qoa:"X-Goog-Meeting-ABR",
Roa:"X-Goog-Meeting-Botguardid",Soa:"X-Goog-Meeting-Bot-Info",Toa:"X-Goog-Meeting-ClientInfo",Uoa:"X-Goog-Meeting-ClientVersion",Voa:"X-Goog-Meeting-Debugid",Woa:"X-Goog-Meeting-Identifier",Xoa:"X-Goog-Meeting-Interop-Cohorts",Yoa:"X-Goog-Meeting-Interop-Type",Zoa:"X-Goog-Meeting-OidcIdToken",apa:"X-Goog-Meeting-RtcClient",bpa:"X-Goog-Meeting-StartSource",cpa:"X-Goog-Meeting-Token",dpa:"X-Goog-Meeting-Viewer-Token",epa:"X-Goog-PageId",gpa:"X-Goog-Safety-Content-Type",hpa:"X-Goog-Safety-Encoding",
Loa:"X-Goog-Drive-Client-Version",Moa:"X-Goog-Drive-Resource-Keys",ipa:"X-HTTP-Method-Override",jpa:"X-JavaScript-User-Agent",kpa:"X-Origin",lpa:"X-Referer",mpa:"X-Requested-With",ppa:"X-Use-HTTP-Status-Code-Override",npa:"X-Server-Timeout",Poa:"X-Goog-First-Party-Reauth",opa:"X-Server-Token",Koa:"x-goog-chat-space-id",fpa:"x-goog-pan-request-context",Doa:"X-AppInt-Credentials",Noa:"X-Goog-Earth-Gcp-Project"},Vg="Accept Accept-Language Authorization Cache-Control cast-device-capabilities Content-Disposition Content-Encoding Content-Language Content-Length Content-MD5 Content-Range Content-Transfer-Encoding Content-Type Date developer-token EES-S7E-MODE financial-institution-id GData-Version google-cloud-resource-prefix hotrod-board-name hotrod-chrome-cpu-model hotrod-chrome-processors Host If-Match If-Modified-Since If-None-Match If-Unmodified-Since linked-customer-id login-customer-id MIME-Version Origin OriginToken Pragma Range request-id Slug Transfer-Encoding Want-Digest X-Ad-Manager-Impersonation X-Ad-Manager-Debug-Info x-alkali-account-key x-alkali-application-key x-alkali-auth-apps-namespace x-alkali-auth-entities-namespace x-alkali-auth-entity x-alkali-client-locale x-chrome-connected x-framework-xsrf-token X-Client-Data X-Client-Pctx X-ClientDetails X-Client-Version x-debug-settings-metadata X-Firebase-Locale X-GData-Client X-GData-Key X-Goog-AuthUser X-Goog-PageId X-Goog-Encode-Response-If-Executable X-GoogApps-Allowed-Domains X-Goog-AdX-Buyer-Impersonation X-Goog-Api-Client X-Goog-Api-Key X-Google-EOM X-Goog-Visibilities X-Goog-Correlation-Id X-Goog-Request-Info X-Goog-Request-Reason X-Goog-Request-Time X-Goog-Experiments x-goog-ext-*********-jspb x-goog-ext-*********-jspb x-goog-ext-*********-jspb x-goog-ext-*********-jspb x-goog-ext-*********-jspb x-goog-ext-*********-jspb x-goog-ext-*********-jspb x-goog-ext-*********-bin x-goog-ext-*********-bin x-goog-ext-*********-jspb x-goog-ext-*********-bin x-goog-ext-*********-bin x-goog-ext-*********-bin x-goog-ext-*********-bin x-goog-ext-*********-bin x-goog-ext-*********-bin x-goog-ext-*********-jspb x-goog-ext-*********-bin X-Goog-Firebase-Installations-Auth x-goog-greenenergyuserappservice-metadata X-Firebase-Client X-Firebase-Client-Log-Type X-Firebase-GMPID X-Firebase-Auth-Token X-Firebase-AppCheck X-Firebase-Token X-Goog-Drive-Client-Version X-Goog-Drive-Resource-Keys x-goog-iam-authority-selector x-goog-iam-authorization-token x-goog-request-params x-goog-sherlog-context X-Goog-Sn-Metadata X-Goog-Sn-PatientId X-Goog-Spatula X-Goog-Travel-Bgr X-Goog-Travel-Settings X-Goog-Upload-Command X-Goog-Upload-Content-Disposition X-Goog-Upload-Content-Length X-Goog-Upload-Content-Type X-Goog-Upload-File-Name X-Goog-Upload-Header-Content-Encoding X-Goog-Upload-Header-Content-Length X-Goog-Upload-Header-Content-Type X-Goog-Upload-Header-Transfer-Encoding X-Goog-Upload-Offset X-Goog-Upload-Protocol X-Goog-User-Project X-Goog-Visitor-Id X-Goog-FieldMask X-Google-Project-Override x-goog-maps-api-salt x-goog-maps-api-signature x-goog-maps-client-id x-goog-maps-channel-id x-goog-maps-solution-id x-goog-maps-session-id x-goog-maps-traffic-policy x-goog-gmp-client-signals x-goog-spanner-database-role X-HTTP-Method-Override X-JavaScript-User-Agent X-Pan-Versionid X-Proxied-User-IP X-Origin X-Referer X-Requested-With X-Stadia-Client-Context X-Upload-Content-Length X-Upload-Content-Type X-Use-Alt-Service X-Use-HTTP-Status-Code-Override X-Ios-Bundle-Identifier X-Places-Ios-Sdk X-Android-Package X-Android-Cert X-Places-Android-Sdk X-Goog-Maps-Ios-Uuid X-Goog-Maps-Android-Uuid X-Ariane-Xsrf-Token X-Earth-Engine-App-ID-Token X-Earth-Engine-Computation-Profile X-Earth-Engine-Computation-Profiling X-Play-Console-Experiments-Override X-Play-Console-Session-Id X-YouTube-Bootstrap-Logged-In X-Youtube-Client-Version X-Youtube-Lava-Device-Context X-YouTube-VVT X-YouTube-Page-CL X-YouTube-Page-Label X-YouTube-Page-Timestamp X-Compass-Routing-Destination X-Goog-Meeting-ABR X-Goog-Meeting-Botguardid X-Goog-Meeting-Bot-Info X-Goog-Meeting-ClientInfo X-Goog-Meeting-ClientVersion X-Goog-Meeting-Debugid X-Goog-Meeting-Identifier X-Goog-Meeting-Interop-Cohorts X-Goog-Meeting-Interop-Type X-Goog-Meeting-OidcIdToken X-Goog-Meeting-RtcClient X-Goog-Meeting-StartSource X-Goog-Meeting-Token X-Goog-Meeting-Viewer-Token x-sdm-id-token X-Sfdc-Authorization X-Server-Timeout x-foyer-client-environment X-Goog-First-Party-Reauth X-Server-Token x-rfui-request-context x-goog-chat-space-id x-goog-nest-jwt X-Cloud-Trace-Context traceparent x-goog-pan-request-context X-AppInt-Credentials X-Goog-Earth-Gcp-Project".split(" "),
Wg="Digest Cache-Control Content-Disposition Content-Encoding Content-Language Content-Length Content-MD5 Content-Range Content-Transfer-Encoding Content-Type Date ETag Expires Last-Modified Location Pragma Range Server Transfer-Encoding WWW-Authenticate Vary Unzipped-Content-MD5 X-Correlation-ID X-Debug-Tracking-Id X-Google-Consent X-Google-EOM X-Goog-Generation X-Goog-Metageneration X-Goog-Safety-Content-Type X-Goog-Safety-Encoding X-Google-Trace X-Goog-Upload-Chunk-Granularity X-Goog-Upload-Control-URL X-Goog-Upload-Size-Received X-Goog-Upload-Status X-Goog-Upload-URL X-Goog-Diff-Download-Range X-Goog-Hash X-Goog-Updated-Authorization X-Server-Object-Version X-Guploader-Customer X-Guploader-Upload-Result X-Guploader-Uploadid X-Google-Gfe-Backend-Request-Cost X-Earth-Engine-Computation-Profile X-Cloudaicompanion-Trace-Id X-Goog-Meeting-ABR X-Goog-Meeting-Botguardid X-Goog-Meeting-Bot-Info X-Goog-Meeting-ClientInfo X-Goog-Meeting-ClientVersion X-Goog-Meeting-Debugid X-Goog-Meeting-Identifier X-Goog-Meeting-RtcClient X-Goog-Meeting-Token X-Goog-Meeting-Viewer-Token X-Compass-Routing-Destination".split(" ");var Xg,Yg,Zg,$g,bh,ch,dh,eh,fh,gh,hh,ih;Xg=null;Yg=null;Zg=null;$g=function(a,b){var c=a.length;if(c!=b.length)return!1;for(var d=0;d<c;++d){var e=a.charCodeAt(d),f=b.charCodeAt(d);e>=65&&e<=90&&(e+=32);f>=65&&f<=90&&(f+=32);if(e!=f)return!1}return!0};
_.ah=function(a){a=String(a||"").split("\x00").join("");for(var b=[],c=!0,d=a.length,e=0;e<d;++e){var f=a.charAt(e),h=a.charCodeAt(e);if(h>=55296&&h<=56319&&e+1<d){var k=a.charAt(e+1),l=a.charCodeAt(e+1);l>=56320&&l<=57343&&(f+=k,h=65536+(h-55296<<10)+(l-56320),++e)}if(!(h>=0&&h<=1114109)||h>=55296&&h<=57343||h>=64976&&h<=65007||(h&65534)==65534)h=65533,f=String.fromCharCode(h);k=!(h>=32&&h<=126)||f==" "||c&&f==":"||f=="\\";!c||f!="/"&&f!="?"||(c=!1);f=="%"&&(e+2>=d?k=!0:(l=16*parseInt(a.charAt(e+
1),16)+parseInt(a.charAt(e+2),16),l>=0&&l<=255?(h=l,f=h==0?"":"%"+(256+l).toString(16).toUpperCase().substr(1),e+=2):k=!0));k&&(f=encodeURIComponent(f),f.length<=1&&(h>=0&&h<=127?f="%"+(256+h).toString(16).toUpperCase().substr(1):(h=65533,f=encodeURIComponent(String.fromCharCode(h)))));b.push(f)}a=b.join("");a=a.split("#")[0];a=a.split("?");b=a[0].split("/");c=[];d=b.length;for(e=0;e<d;++e)f=b[e],h=f.split("%2E").join("."),h=h.split(encodeURIComponent("\uff0e")).join("."),h=="."?e+1==d&&c.push(""):
h==".."?(c.length>0&&c.pop(),e+1==d&&c.push("")):c.push(f);a[0]=c.join("/");for(a=a.join("?");a&&a.charAt(0)=="/";)a=a.substr(1);return"/"+a};bh={"access-control-allow-origin":!0,"access-control-allow-credentials":!0,"access-control-expose-headers":!0,"access-control-max-age":!0,"access-control-allow-headers":!0,"access-control-allow-methods":!0,p3p:!0,"proxy-authenticate":!0,"set-cookie":!0,"set-cookie2":!0,status:!0,tsv:!0,"":!0};
ch={"accept-charset":!0,"accept-encoding":!0,"access-control-request-headers":!0,"access-control-request-method":!0,"client-ip":!0,clientip:!0,connection:!0,"content-length":!0,cookie:!0,cookie2:!0,date:!0,dnt:!0,expect:!0,forwarded:!0,"forwarded-for":!0,"front-end-https":!0,host:!0,"keep-alive":!0,"max-forwards":!0,method:!0,origin:!0,"raw-post-data":!0,referer:!0,te:!0,trailer:!0,"transfer-encoding":!0,upgrade:!0,url:!0,"user-agent":!0,version:!0,via:!0,"x-att-deviceid":!0,"x-chrome-connected":!0,
"x-client-data":!0,"x-client-ip":!0,"x-do-not-track":!0,"x-forwarded-by":!0,"x-forwarded-for":!0,"x-forwarded-host":!0,"x-forwarded-proto":!0,"x-geo":!0,"x-googapps-allowed-domains":!0,"x-origin":!0,"x-proxyuser-ip":!0,"x-real-ip":!0,"x-referer":!0,"x-uidh":!0,"x-user-ip":!0,"x-wap-profile":!0,"":!0};
dh=function(a){if(!_.hd(a))return null;for(var b={},c=0;c<a.length;c++){var d=a[c];if(typeof d==="string"&&d){var e=d.toLowerCase();$g(d,e)&&(b[e]=d)}}for(var f in Ug)Object.prototype.hasOwnProperty.call(Ug,f)&&(a=Ug[f],c=a.toLowerCase(),$g(a,c)&&Object.prototype.hasOwnProperty.call(b,c)&&(b[c]=a));return b};eh=new RegExp("("+/[\t -~\u00A0-\u2027\u202A-\uD7FF\uE000-\uFFFF]/.source+"|"+/[\uD800-\uDBFF][\uDC00-\uDFFF]/.source+"){1,100}","g");fh=/[ \t]*(\r?\n[ \t]+)+/g;gh=/^[ \t]+|[ \t]+$/g;
hh=function(a,b){if(!b&&typeof a==="object"&&a&&typeof a.length==="number"){b=a;a="";for(var c=b.length,d=0;d<c;++d){var e=hh(b[d],!0);e&&(a&&(e=a+", "+e),a=e)}}if(typeof a==="string"&&(a=a.replace(fh," "),a=a.replace(gh,""),a.replace(eh,"")==""&&a))return a};ih=/^[-0-9A-Za-z!#\$%&'\*\+\.\^_`\|~]+$/g;
_.jh=function(a){if(typeof a!=="string"||!a||!a.match(ih))return null;a=a.toLowerCase();if(Zg==null){var b=[],c=_.Ue("googleapis/headers/response");c&&typeof c==="object"&&typeof c.length==="number"||(c=null);c!=null&&(b=b.concat(c));(c=_.Ue("client/headers/response"))&&typeof c==="object"&&typeof c.length==="number"||(c=null);c!=null&&(b=b.concat(c));b=b.concat(Wg);(c=_.Ue("googleapis/headers/request"))&&typeof c==="object"&&typeof c.length==="number"||(c=null);c!=null&&(b=b.concat(c));(c=_.Ue("client/headers/request"))&&
typeof c==="object"&&typeof c.length==="number"||(c=null);c!=null&&(b=b.concat(c));b=b.concat(Vg);for(var d in Ug)Object.prototype.hasOwnProperty.call(Ug,d)&&b.push(Ug[d]);Zg=dh(b)}return Zg!=null&&Zg.hasOwnProperty(a)?Zg[a]:a};
_.kh=function(a,b){if(!_.jh(a)||!hh(b))return null;a=a.toLowerCase();if(a.match(/^x-google|^x-gfe|^proxy-|^sec-/i)||ch[a])return null;if(Xg==null){b=[];var c=_.Ue("googleapis/headers/request");c&&typeof c==="object"&&typeof c.length==="number"||(c=null);c!=null&&(b=b.concat(c));(c=_.Ue("client/headers/request"))&&typeof c==="object"&&typeof c.length==="number"||(c=null);c!=null&&(b=b.concat(c));b=b.concat(Vg);Xg=dh(b)}return Xg!=null&&Xg.hasOwnProperty(a)?Xg[a]:null};
_.lh=function(a,b){if(!_.jh(a)||!hh(b))return null;a=a.toLowerCase();if(bh[a])return null;if(Yg==null){b=[];var c=_.Ue("googleapis/headers/response");c&&typeof c==="object"&&typeof c.length==="number"||(c=null);c!=null&&(b=b.concat(c));(c=_.Ue("client/headers/response"))&&typeof c==="object"&&typeof c.length==="number"||(c=null);c!=null&&(b=b.concat(c));b=b.concat(Wg);Yg=dh(b)}return Yg!=null&&Yg.hasOwnProperty(a)?a:null};
_.mh=function(a,b){if(_.jh(b)&&a!=null&&typeof a==="object"){var c=void 0,d;for(d in a)if(Object.prototype.hasOwnProperty.call(a,d)&&$g(d,b)){var e=hh(a[d]);e&&(c!==void 0&&(e=c+", "+e),c=e)}return c}};_.nh=function(a,b,c,d){var e=_.jh(b);if(e){c&&(c=hh(c));b=b.toLowerCase();for(var f in a)Object.prototype.hasOwnProperty.call(a,f)&&$g(f,b)&&delete a[f];c&&(d||(b=e),a[b]=c)}};
_.oh=function(a,b){var c={};if(!a)return c;a=a.split("\r\n");for(var d=a.length,e=0;e<d;++e){var f=a[e];if(!f)break;var h=f.indexOf(":");if(!(h<=0)){var k=f.substring(0,h);if(k=_.jh(k)){for(f=f.substring(h+1);e+1<d&&a[e+1].match(/^[ \t]/);)f+="\r\n"+a[e+1],++e;if(f=hh(f))if(k=_.lh(k,f)||(b?void 0:k))k=k.toLowerCase(),h=_.mh(c,k),h!==void 0&&(f=h+", "+f),_.nh(c,k,f,!0)}}}return c};
/\uffff/.test("\uffff");
var wy;_.vy=function(a){var b=0,c;for(c in a)b++;return b};wy=function(a,b){var c=[];for(b=b||0;b<a.length;b+=2)_.Sh(a[b],a[b+1],c);return c.join("&")};_.xy=function(a,b){var c=arguments.length==2?wy(arguments[1],0):wy(arguments,1);return _.Rh(a,c)};_.yy=function(a,b){_.Kj(a,"/")&&(a=a.slice(0,-1));_.vc(b,"/")&&(b=b.slice(1));return a+"/"+b};_.zy=function(a){switch(a){case 200:case 201:case 202:case 204:case 206:case 304:case 1223:return!0;default:return!1}};var By,Cy,Dy;_.Ay=function(a){_.Mj.call(this);this.headers=new Map;this.T1=a||null;this.Xf=!1;this.Va=null;this.cB="";this.jr=0;this.Bo=this.VH=this.CA=this.VF=!1;this.Fs=0;this.Qc=null;this.Rm="";this.yh=!1;this.HE=this.zN=null};_.eb(_.Ay,_.Mj);_.Ay.prototype.Ab=null;By=/^https?$/i;Cy=["POST","PUT"];Dy=[];_.Ey=function(a,b,c,d,e,f,h){var k=new _.Ay;Dy.push(k);b&&k.na("complete",b);k.qr("ready",k.y7);f&&k.hD(f);h&&(k.yh=h);k.send(a,c,d,e)};_.g=_.Ay.prototype;
_.g.y7=function(){this.dispose();_.ej(Dy,this)};_.g.hD=function(a){this.Fs=Math.max(0,a)};_.g.setTrustToken=function(a){this.zN=a};_.g.setAttributionReporting=function(a){this.HE=a};
_.g.send=function(a,b,c,d){if(this.Va)throw Error("ua`"+this.cB+"`"+a);b=b?b.toUpperCase():"GET";this.cB=a;this.jr=0;this.VF=!1;this.Xf=!0;this.Va=this.T1?this.T1.Ct():_.ty.Ct();this.Va.onreadystatechange=(0,_.ok)((0,_.z)(this.GX,this));try{this.VH=!0,this.Va.open(b,String(a),!0),this.VH=!1}catch(h){this.fz(5,h);return}a=c||"";c=new Map(this.headers);if(d)if(Object.getPrototypeOf(d)===Object.prototype)for(var e in d)c.set(e,d[e]);else if(typeof d.keys==="function"&&typeof d.get==="function"){e=_.Ca(d.keys());
for(var f=e.next();!f.done;f=e.next())f=f.value,c.set(f,d.get(f))}else throw Error("va`"+String(d));d=Array.from(c.keys()).find(function(h){return"content-type"==h.toLowerCase()});e=_.Xa.FormData&&a instanceof _.Xa.FormData;!_.tb(Cy,b)||d||e||c.set("Content-Type","application/x-www-form-urlencoded;charset=utf-8");b=_.Ca(c);for(d=b.next();!d.done;d=b.next())c=_.Ca(d.value),d=c.next().value,c=c.next().value,this.Va.setRequestHeader(d,c);this.Rm&&(this.Va.responseType=this.Rm);"withCredentials"in this.Va&&
this.Va.withCredentials!==this.yh&&(this.Va.withCredentials=this.yh);if("setTrustToken"in this.Va&&this.zN)try{this.Va.setTrustToken(this.zN)}catch(h){}if("setAttributionReporting"in this.Va&&this.HE)try{this.Va.setAttributionReporting(this.HE)}catch(h){}try{this.Qc&&(clearTimeout(this.Qc),this.Qc=null),this.Fs>0&&(this.Qc=setTimeout(this.Ii.bind(this),this.Fs)),this.CA=!0,this.Va.send(a),this.CA=!1}catch(h){this.fz(5,h)}};
_.g.Ii=function(){typeof _.Va!="undefined"&&this.Va&&(this.jr=8,this.dispatchEvent("timeout"),this.abort(8))};_.g.fz=function(a){this.Xf=!1;this.Va&&(this.Bo=!0,this.Va.abort(),this.Bo=!1);this.jr=a;Fy(this);Gy(this)};var Fy=function(a){a.VF||(a.VF=!0,a.dispatchEvent("complete"),a.dispatchEvent("error"))};_.Ay.prototype.abort=function(a){this.Va&&this.Xf&&(this.Xf=!1,this.Bo=!0,this.Va.abort(),this.Bo=!1,this.jr=a||7,this.dispatchEvent("complete"),this.dispatchEvent("abort"),Gy(this))};
_.Ay.prototype.ua=function(){this.Va&&(this.Xf&&(this.Xf=!1,this.Bo=!0,this.Va.abort(),this.Bo=!1),Gy(this,!0));_.Ay.N.ua.call(this)};_.Ay.prototype.GX=function(){this.isDisposed()||(this.VH||this.CA||this.Bo?Hy(this):this.yJ())};_.Ay.prototype.yJ=function(){Hy(this)};
var Hy=function(a){if(a.Xf&&typeof _.Va!="undefined")if(a.CA&&_.Iy(a)==4)setTimeout(a.GX.bind(a),0);else if(a.dispatchEvent("readystatechange"),_.Iy(a)==4){a.Xf=!1;try{a.ir()?(a.dispatchEvent("complete"),a.dispatchEvent("success")):(a.jr=6,a.getStatus(),Fy(a))}finally{Gy(a)}}},Gy=function(a,b){if(a.Va){a.Qc&&(clearTimeout(a.Qc),a.Qc=null);var c=a.Va;a.Va=null;b||a.dispatchEvent("ready");try{c.onreadystatechange=null}catch(d){}}};_.Ay.prototype.isActive=function(){return!!this.Va};
_.Ay.prototype.ir=function(){var a=this.getStatus(),b;if(!(b=_.zy(a))){if(a=a===0)a=String(this.cB).match(_.Qh)[1]||null,!a&&_.Xa.self&&_.Xa.self.location&&(a=_.Xa.self.location.protocol.slice(0,-1)),a=!By.test(a?a.toLowerCase():"");b=a}return b};_.Iy=function(a){return a.Va?a.Va.readyState:0};_.Ay.prototype.getStatus=function(){try{return _.Iy(this)>2?this.Va.status:-1}catch(a){return-1}};_.Jy=function(a){try{return a.Va?a.Va.responseText:""}catch(b){return""}};
_.Ky=function(a){try{if(!a.Va)return null;if("response"in a.Va)return a.Va.response;switch(a.Rm){case "":case "text":return a.Va.responseText;case "arraybuffer":if("mozResponseArrayBuffer"in a.Va)return a.Va.mozResponseArrayBuffer}return null}catch(b){return null}};_.Ay.prototype.getResponseHeader=function(a){if(this.Va&&_.Iy(this)==4)return a=this.Va.getResponseHeader(a),a===null?void 0:a};
_.Ay.prototype.getAllResponseHeaders=function(){return this.Va&&_.Iy(this)>=2?this.Va.getAllResponseHeaders()||"":""};_.kj(function(a){_.Ay.prototype.yJ=a(_.Ay.prototype.yJ)});
var vu,Au;_.ru=function(a,b){var c=_.hd(b),d=c?b:arguments;for(c=c?0:1;c<d.length;c++){if(a==null)return;a=a[d[c]]}return a};
_.su=function(a){if(!a||typeof a!=="object")return a;if(typeof a.clone==="function")return a.clone();if(typeof Map!=="undefined"&&a instanceof Map)return new Map(a);if(typeof Set!=="undefined"&&a instanceof Set)return new Set(a);if(a instanceof Date)return new Date(a.getTime());var b=Array.isArray(a)?[]:typeof ArrayBuffer!=="function"||typeof ArrayBuffer.isView!=="function"||!ArrayBuffer.isView(a)||a instanceof DataView?{}:new a.constructor(a.length),c;for(c in a)b[c]=_.su(a[c]);return b};
_.tu=function(){return Math.floor(Math.random()*2147483648).toString(36)+Math.abs(Math.floor(Math.random()*2147483648)^_.id()).toString(36)};_.uu=function(a,b,c){return _.ie(document,arguments)};vu=function(a,b){if(a){a=a.split("&");for(var c=0;c<a.length;c++){var d=a[c].indexOf("="),e=null;if(d>=0){var f=a[c].substring(0,d);e=a[c].substring(d+1)}else f=a[c];b(f,e?decodeURIComponent(e.replace(/\+/g," ")):"")}}};
_.wu=function(a,b,c){for(var d=0,e=b.length;(d=a.indexOf(b,d))>=0&&d<c;){var f=a.charCodeAt(d-1);if(f==38||f==63)if(f=a.charCodeAt(d+e),!f||f==61||f==38||f==35)return d;d+=e+1}return-1};_.xu=/#|$/;_.yu=function(a){if(a.Ze&&typeof a.Ze=="function")return a.Ze();if(typeof Map!=="undefined"&&a instanceof Map||typeof Set!=="undefined"&&a instanceof Set)return Array.from(a.values());if(typeof a==="string")return a.split("");if(_.hd(a)){for(var b=[],c=a.length,d=0;d<c;d++)b.push(a[d]);return b}return _.ub(a)};
_.zu=function(a){if(a.lg&&typeof a.lg=="function")return a.lg();if(!a.Ze||typeof a.Ze!="function"){if(typeof Map!=="undefined"&&a instanceof Map)return Array.from(a.keys());if(!(typeof Set!=="undefined"&&a instanceof Set)){if(_.hd(a)||typeof a==="string"){var b=[];a=a.length;for(var c=0;c<a;c++)b.push(c);return b}return _.Nh(a)}}};
Au=function(a,b,c){if(a.forEach&&typeof a.forEach=="function")a.forEach(b,c);else if(_.hd(a)||typeof a==="string")Array.prototype.forEach.call(a,b,c);else for(var d=_.zu(a),e=_.yu(a),f=e.length,h=0;h<f;h++)b.call(c,e[h],d&&d[h],a)};var Ou,Iu,Su,Ku,Ju,Mu,Lu,Pu,Nu,Tu;
_.Bu=function(a,b){this.Zd=this.xh=this.Ci="";this.Cg=null;this.qG=this.Lm="";this.Ug=!1;var c;a instanceof _.Bu?(this.Ug=b!==void 0?b:a.Ug,_.Cu(this,a.Ci),_.Du(this,a.xh),_.Eu(this,a.Og()),_.Fu(this,a.Cg),this.setPath(a.getPath()),_.Gu(this,a.Rd.clone()),this.Sk(a.Gz())):a&&(c=String(a).match(_.Qh))?(this.Ug=!!b,_.Cu(this,c[1]||"",!0),_.Du(this,c[2]||"",!0),_.Eu(this,c[3]||"",!0),_.Fu(this,c[4]),this.setPath(c[5]||"",!0),_.Gu(this,c[6]||"",!0),this.Sk(c[7]||"",!0)):(this.Ug=!!b,this.Rd=new _.Hu(null,
this.Ug))};_.Bu.prototype.toString=function(){var a=[],b=this.Ci;b&&a.push(Iu(b,Ju,!0),":");var c=this.Og();if(c||b=="file")a.push("//"),(b=this.xh)&&a.push(Iu(b,Ju,!0),"@"),a.push(Ku(encodeURIComponent(String(c)))),c=this.Cg,c!=null&&a.push(":",String(c));if(c=this.getPath())this.Zd&&c.charAt(0)!="/"&&a.push("/"),a.push(Iu(c,c.charAt(0)=="/"?Lu:Mu,!0));(c=this.Rd.toString())&&a.push("?",c);(c=this.Gz())&&a.push("#",Iu(c,Nu));return a.join("")};
_.Bu.prototype.resolve=function(a){var b=this.clone(),c=!!a.Ci;c?_.Cu(b,a.Ci):c=!!a.xh;c?_.Du(b,a.xh):c=!!a.Zd;c?_.Eu(b,a.Og()):c=a.Cg!=null;var d=a.getPath();if(c)_.Fu(b,a.Cg);else if(c=!!a.Lm){if(d.charAt(0)!="/")if(this.Zd&&!this.Lm)d="/"+d;else{var e=b.getPath().lastIndexOf("/");e!=-1&&(d=b.getPath().slice(0,e+1)+d)}e=d;if(e==".."||e==".")d="";else if(_.xc(e,"./")||_.xc(e,"/.")){d=_.vc(e,"/");e=e.split("/");for(var f=[],h=0;h<e.length;){var k=e[h++];k=="."?d&&h==e.length&&f.push(""):k==".."?((f.length>
1||f.length==1&&f[0]!="")&&f.pop(),d&&h==e.length&&f.push("")):(f.push(k),d=!0)}d=f.join("/")}else d=e}c?b.setPath(d):c=a.Sq();c?_.Gu(b,a.Rd.clone()):c=!!a.qG;c&&b.Sk(a.Gz());return b};_.Bu.prototype.clone=function(){return new _.Bu(this)};_.Cu=function(a,b,c){a.Ci=c?Ou(b,!0):b;a.Ci&&(a.Ci=a.Ci.replace(/:$/,""));return a};_.Du=function(a,b,c){a.xh=c?Ou(b):b;return a};_.Bu.prototype.Og=function(){return this.Zd};_.Eu=function(a,b,c){a.Zd=c?Ou(b,!0):b;return a};
_.Fu=function(a,b){if(b){b=Number(b);if(isNaN(b)||b<0)throw Error("M`"+b);a.Cg=b}else a.Cg=null;return a};_.Bu.prototype.getPath=function(){return this.Lm};_.Bu.prototype.setPath=function(a,b){this.Lm=b?Ou(a,!0):a;return this};_.Bu.prototype.Sq=function(){return this.Rd.toString()!==""};_.Gu=function(a,b,c){b instanceof _.Hu?(a.Rd=b,a.Rd.HL(a.Ug)):(c||(b=Iu(b,Pu)),a.Rd=new _.Hu(b,a.Ug));return a};_.Bu.prototype.hb=function(a,b){return _.Gu(this,a,b)};_.Bu.prototype.getQuery=function(){return this.Rd.toString()};
_.Qu=function(a,b,c){a.Rd.set(b,c);return a};_.g=_.Bu.prototype;_.g.Qg=function(a){return this.Rd.get(a)};_.g.Gz=function(){return this.qG};_.g.Sk=function(a,b){this.qG=b?Ou(a):a;return this};_.g.removeParameter=function(a){this.Rd.remove(a);return this};_.g.HL=function(a){this.Ug=a;this.Rd&&this.Rd.HL(a)};_.Ru=function(a,b){return a instanceof _.Bu?a.clone():new _.Bu(a,b)};Ou=function(a,b){return a?b?decodeURI(a.replace(/%25/g,"%2525")):decodeURIComponent(a):""};
Iu=function(a,b,c){return typeof a==="string"?(a=encodeURI(a).replace(b,Su),c&&(a=Ku(a)),a):null};Su=function(a){a=a.charCodeAt(0);return"%"+(a>>4&15).toString(16)+(a&15).toString(16)};Ku=function(a){return a.replace(/%25([0-9a-fA-F]{2})/g,"%$1")};Ju=/[#\/\?@]/g;Mu=/[#\?:]/g;Lu=/[#\?]/g;Pu=/[#\?@]/g;Nu=/#/g;_.Hu=function(a,b){this.Be=this.Lc=null;this.jg=a||null;this.Ug=!!b};Tu=function(a){a.Lc||(a.Lc=new Map,a.Be=0,a.jg&&vu(a.jg,function(b,c){a.add(decodeURIComponent(b.replace(/\+/g," ")),c)}))};
_.g=_.Hu.prototype;_.g.Yb=function(){Tu(this);return this.Be};_.g.add=function(a,b){Tu(this);this.jg=null;a=Uu(this,a);var c=this.Lc.get(a);c||this.Lc.set(a,c=[]);c.push(b);this.Be+=1;return this};_.g.remove=function(a){Tu(this);a=Uu(this,a);return this.Lc.has(a)?(this.jg=null,this.Be-=this.Lc.get(a).length,this.Lc.delete(a)):!1};_.g.clear=function(){this.Lc=this.jg=null;this.Be=0};_.g.isEmpty=function(){Tu(this);return this.Be==0};_.g.Dl=function(a){Tu(this);a=Uu(this,a);return this.Lc.has(a)};
_.g.forEach=function(a,b){Tu(this);this.Lc.forEach(function(c,d){c.forEach(function(e){a.call(b,e,d,this)},this)},this)};_.g.lg=function(){Tu(this);for(var a=Array.from(this.Lc.values()),b=Array.from(this.Lc.keys()),c=[],d=0;d<b.length;d++)for(var e=a[d],f=0;f<e.length;f++)c.push(b[d]);return c};_.g.Ze=function(a){Tu(this);var b=[];if(typeof a==="string")this.Dl(a)&&(b=b.concat(this.Lc.get(Uu(this,a))));else{a=Array.from(this.Lc.values());for(var c=0;c<a.length;c++)b=b.concat(a[c])}return b};
_.g.set=function(a,b){Tu(this);this.jg=null;a=Uu(this,a);this.Dl(a)&&(this.Be-=this.Lc.get(a).length);this.Lc.set(a,[b]);this.Be+=1;return this};_.g.get=function(a,b){if(!a)return b;a=this.Ze(a);return a.length>0?String(a[0]):b};_.g.setValues=function(a,b){this.remove(a);b.length>0&&(this.jg=null,this.Lc.set(Uu(this,a),_.Vb(b)),this.Be+=b.length)};
_.g.toString=function(){if(this.jg)return this.jg;if(!this.Lc)return"";for(var a=[],b=Array.from(this.Lc.keys()),c=0;c<b.length;c++){var d=b[c],e=encodeURIComponent(String(d));d=this.Ze(d);for(var f=0;f<d.length;f++){var h=e;d[f]!==""&&(h+="="+encodeURIComponent(String(d[f])));a.push(h)}}return this.jg=a.join("&")};_.g.clone=function(){var a=new _.Hu;a.jg=this.jg;this.Lc&&(a.Lc=new Map(this.Lc),a.Be=this.Be);return a};var Uu=function(a,b){b=String(b);a.Ug&&(b=b.toLowerCase());return b};
_.Hu.prototype.HL=function(a){a&&!this.Ug&&(Tu(this),this.jg=null,this.Lc.forEach(function(b,c){var d=c.toLowerCase();c!=d&&(this.remove(c),this.setValues(d,b))},this));this.Ug=a};_.Hu.prototype.extend=function(a){for(var b=0;b<arguments.length;b++)Au(arguments[b],function(c,d){this.add(d,c)},this)};
var YA=function(a){if(!a||typeof a!=="function")throw new XA("Must provide a function.");this.Dg=null;this.G8=a},ZA=!1,kB,lB,mB,nB,oB,pB,qB,rB,sB,tB,uB,vB,wB,xB,yB;ZA=!1;
var $A=function(a){return new _.xk(function(b){var c=a.length,d=[];if(c)for(var e=function(k,l,m){c--;d[k]=l?{qz:!0,value:m}:{qz:!1,reason:m};c==0&&b(d)},f,h=0;h<a.length;h++)f=a[h],_.Ek(f,_.bb(e,h,!0),_.bb(e,h,!1));else b(d)})},aB,bB,cB,dB={GP:function(a){aB=a;try{delete dB.GP}catch(b){}},HP:function(a){bB=a;try{delete dB.HP}catch(b){}},IP:function(a){cB=a;try{delete dB.IP}catch(b){}}},eB=function(a){return _.zy(a.status)},fB=function(){var a=!0,b=_.ty.Ct();b&&b.withCredentials!==void 0||(a=!1);
return a},gB=function(a,b){if(b==null)return b;b=String(b);b.match(/^\/\/.*/)&&(b=(window.location.protocol=="http:"?"http:":"https:")+b);b.match(/^\/([^\/].*)?$/)&&window.location.host&&String(window.location.protocol).match(/^https?:$/)&&(b=window.location.protocol+"//"+window.location.host+b);var c=b.match(/^(https?:)(\/\/)?(\/([^\/].*)?)?$/i);c&&window.location.host&&String(window.location.protocol).match(/^https?:$/)&&(b=c[1]+"//"+window.location.host+(c[3]||""));b=b.replace(/^(https?:\/\/[^\/?#@]*)\/$/i,
"$1");b=b.replace(/^(http:\/\/[-_a-z0-9.]+):0*80([\/?#].*)?$/i,"$1$2");b=b.replace(/^(https:\/\/[-_a-z0-9.]+):0*443([\/?#].*)?$/i,"$1$2");b.match(/^https?:\/\/[-_a-z0-9.]*[-_a-z][-_a-z0-9.]*$/i)&&(b=b.toLowerCase());c=_.Ue("client/rewrite");_.vb(c)&&Object.prototype.hasOwnProperty.call(c,b)?b=String(c[b]||b):(b=b.replace(/^(https?):\/\/www\.googleapis\.com$/,"$1://content.googleapis.com"),b=b.replace(/^(https?):\/\/www-(googleapis-[-_a-z0-9]+\.[-_a-z0-9]+\.google\.com)$/,"$1://content-$2"),b.match(/^https?:\/\/content(-[-_a-z0-9.]+)?\.googleapis\.com$/)||
(b=b.replace(/^(https?):\/\/([-_a-z0-9]+(\.[-_a-z0-9]+)?\.googleapis\.com)$/,"$1://content-$2")));a&&(a=_.Ue("client/firstPartyRewrite"),_.vb(a)&&Object.prototype.hasOwnProperty.call(a,b)?b=String(a[b]||b):(b=b.replace(/^(https?):\/\/content\.googleapis\.com$/,"$1://clients6.google.com"),b=b.replace(/^(https?):\/\/content-([-a-z0-9]+)\.([-a-z0-9]+)\.googleapis\.com$/,"$1://$2-googleapis.$3.google.com"),b=b.replace(/^(https?):\/\/content-([-a-z0-9]+)\.googleapis\.com$/,"$1://$2.clients6.google.com"),
b=b.replace(/^(https?):\/\/([-a-z0-9]+)-www-googleapis\.([-a-z0-9]+).google.com$/,"$1://content-googleapis-$2.$3.google.com")));return b},XA=function(a){_.lb.call(this,a)};_.y(XA,_.lb);XA.prototype.name="gapix.client.GapiClientError";YA.prototype.then=function(a,b,c){this.Dg||(this.Dg=this.G8());return this.Dg.then(a,b,c)};YA.prototype.YC=function(a){this.Dg||(this.Dg=a)};
var hB=function(a){var b={},c;for(c in a)if(Object.prototype.hasOwnProperty.call(a,c)){var d=_.mh(a,c);d&&(c=_.lh(c,d))&&_.nh(b,c,d,!0)}return b},iB={error:{code:-1,message:"A network error occurred and the request could not be completed."}},jB=function(a,b,c,d){_.Ay.call(this);this.Id=a;this.YI=b;this.Ld=c;a={};if(d)for(var e in d)Object.prototype.hasOwnProperty.call(d,e)&&(b=_.mh(d,e),b!==void 0&&(e=_.kh(e,b))&&_.nh(a,e,b));d={};for(var f in a)Object.prototype.hasOwnProperty.call(a,f)&&(d[unescape(encodeURIComponent(f))]=
unescape(encodeURIComponent(a[f])));this.Uu=d;this.Dg=null};_.y(jB,_.Ay);
jB.prototype.then=function(a){this.Dg||(this.Dg=(new _.xk(function(b,c){this.na("error",(0,_.z)(function(){c(kB(this))},this));this.na("success",(0,_.z)(function(){b(kB(this))},this));this.send(this.Id,this.YI,this.Ld,this.Uu)},this)).then(function(b){b.headers=hB(b.headers);return b},function(b){return b.status?(b.headers=hB(b.headers),_.Ck(b)):_.Ck({result:iB,body:'{"error":{"code":-1,"message":"A network error occurred and the request could not be completed."}}',headers:null,status:null,statusText:null})}));
return this.Dg.then.apply(this.Dg,arguments)};kB=function(a){var b=a.getStatus(),c=_.Jy(a);var d=b==204?!1:a.Rm==""?_.Nf(c):_.Ky(a);var e=a.getAllResponseHeaders();e=_.oh(e,!1);try{var f=_.Iy(a)>2?a.Va.statusText:""}catch(h){f=""}return{result:d,body:c,headers:e,status:b,statusText:f}};lB=/;\s*charset\s*=\s*("utf-?8"|utf-?8)\s*(;|$)/i;mB=/^(text\/[^\s;\/""]+|application\/(json(\+[^\s;\/""]*)?|([^\s;\/""]*\+)?xml))\s*(;|$)/i;nB=/;\s*charset\s*=/i;oB=/(([\r\n]{0,2}[A-Za-z0-9+\/]){4,4}){0,1024}([\r\n]{0,2}[A-Za-z0-9+\/][\r\n]{0,2}[AQgw]([\r\n]{0,2}=){2,2}|([\r\n]{0,2}[A-Za-z0-9+\/]){2,2}[\r\n]{0,2}[AEIMQUYcgkosw048][\r\n]{0,2}=|([\r\n]{0,2}[A-Za-z0-9+\/]){4,4})[\r\n]{0,2}/g;
pB=function(a){var b=[];a=a.replace(oB,function(c){b.push(_.Az(c));return""});if(a.length)throw Error("wa");return b.join("")};qB=function(a){var b=a.headers;if(b&&_.mh(b,"X-Goog-Safety-Encoding")==="base64"){var c=pB(a.body),d=_.mh(b,"X-Goog-Safety-Content-Type");b["Content-Type"]=d;if(d.match(lB)||d.match(mB)&&!d.match(nB))c=_.Gh(c),c=_.Fw(c);_.nh(b,"X-Goog-Safety-Encoding");_.nh(b,"X-Goog-Safety-Content-Type");a.body=c}};
rB=function(a,b,c){c||((c=_.Ue("googleapis.config/proxy"))&&(c=String(c).replace(/\/static\/proxy\.html$/,"")||"/"),c=String(c||""));c||(c=_.Ue("googleapis.config/root"),b&&(c=_.Ue("googleapis.config/root-1p")||c),c=String(c||""));c=String(gB(b,c)||c);return a=_.yy(c,a)};
sB=function(a,b){var c=a.params||_.ze();c.url=c.path;var d=c.root;d=rB("/",_.ri(c.headers),d);d.match(/^(.*[^\/])?\/$/)&&(d=d.substr(0,d.length-1));c.root=d;a.params=c;_.Xi.Wm("makeHttpRequests",[a],function(e,f){e&&e.gapiRequest?(e.gapiRequest.data?qB(e.gapiRequest.data):qB(e),b(e,_.Of(e))):b(e,f)})};
tB=function(a){var b=_.ru(a,"params","headers");b&&typeof b==="object"||(b={});a={};for(var c in b)if(Object.prototype.hasOwnProperty.call(b,c)){var d=_.mh(b,c);d&&(_.kh(c,d),_.nh(a,c,d))}c=(window.location.href.match(_.Qh)[1]||null)=="chrome-extension";a=_.ri(a);return!(c&&a)&&fB()};
uB=function(a){return new _.xk(function(b,c){var d=function(e){e&&e.gapiRequest?e=e.gapiRequest.data||e:c(e);e={result:e.status!=204&&_.Nf(e.body),body:e.body,headers:e.headers||null,status:e.status||null,statusText:e.statusText||null};eB(e)?b(e):c(e)};try{sB(a,d)}catch(e){c(e)}})};vB=function(a){var b=!_.Ue("client/cors")||!!_.Ue("client/xd4"),c={};_.Qm(a,function(d,e){(d=_.kh(e,d))||b||(d=_.jh(e));d&&(e=_.mh(a,d))&&_.nh(c,d,e)});return c};
wB=function(a){var b=a.params||_.ze();a=_.fk(b.headers||{});var c=b.httpMethod||"GET",d=String(b.url||""),e=encodeURIComponent("$unique");if(!(c==="POST"||_.wu(d,"$unique",d.search(_.xu))>=0||_.wu(d,e,d.search(_.xu))>=0)){var f=[];for(h in a)Object.prototype.hasOwnProperty.call(a,h)&&f.push(h.toLowerCase());f.sort();f.push(_.Fg(location.href));var h=f.join(":");f=_.ki();f.update(h);h=f.Ti().toLowerCase().substr(0,7);h=String(parseInt(h,16)%1E3+1E3).substr(1);d=_.xy(d,e,"gc"+h)}e=b.body||null;h=b.responseType||
null;b=_.ri(a)||b.authType=="1p";f=!!_.Ue("googleapis.config/auth/useUberProxyAuth")||!!_.Ue("client/withCredentials");_.nh(a,"X-Referer");a=vB(a);var k=new jB(d,c,e,a);k.yh=b||f;h&&(k.Rm=h);return new _.xk(function(l,m){k.then(function(n){qB(n);l(n)},function(n){m(n)})})};xB=function(a,b){var c=function(d){d=_.fk(d);delete d.result;d={gapiRequest:{data:d}};b&&b(d,_.Of(d))};wB(a).then(c,c)};
yB=function(a,b){(_.Ue("client/cors")||_.Ue("client/xd4"))&&tB(a)?(_.Hi(_.Gi(),12).wb(),xB(a,b)):(_.Hi(_.Gi(),11).wb(),sB(a,b))};_.zB={};var AB=function(a){this.fw=a;this.Xf=!1;this.promise={then:(0,_.z)(function(b,c,d){this.Xf||(this.Xf=!0);this.ew&&!this.cw?this.fw.resolve(this.ew):this.cw&&!this.ew&&this.fw.reject(this.cw);return this.fw.promise.then(b,c,d)},this)}};AB.prototype.resolve=function(a){this.Xf?this.fw.resolve(a):this.ew||this.cw||(this.ew=a)};AB.prototype.reject=function(a){this.Xf?this.fw.reject(a):this.ew||this.cw||(this.cw=a)};var BB=function(a){a=_.su(a.error);return{code:a.code,data:a.errors,message:a.message}},CB=function(a){throw Error("Ba`"+a);};var DB=function(a){YA.call(this,DB.prototype.Yo);if(!a||typeof a!="object"&&typeof a!="string")throw new XA("Missing required parameters");if(typeof a==="string"){var b={};b.path=a}else b=a;if(!b.path)throw new XA('Missing required parameter: "path"');this.kh={};this.kh.path=b.path;this.kh.method=b.method||"GET";this.kh.params=b.params||{};this.kh.headers=b.headers||{};this.kh.body=b.body;this.kh.root=b.root;this.kh.responseType=b.responseType;this.kh.apiId=b.apiId;this.tn=b.authType||"auto";this.dca=
!!b.isXd4;this.MV=!1;this.Kj(this.tn);this.jZ=!1};_.y(DB,YA);DB.prototype.Gf=function(){return this.kh};DB.prototype.Kj=function(a){this.tn=a;this.MV=this.tn==="1p"};DB.prototype.Eq=function(){return this.MV};
DB.prototype.Fj=function(){if(!this.jZ){this.jZ=!0;var a=this.kh,b=a.headers=a.headers||{},c=[],d=[];for(h in b)if(Object.prototype.hasOwnProperty.call(b,h)){c.push(h);var e=h,f=_.mh(b,e);f&&(e=_.kh(e,f)||_.jh(e))&&d.push([e,f])}var h=0;for(e=c.length;h<e;++h)delete b[c[h]];c=0;for(h=d.length;c<h;++c)_.nh(b,d[c][0],d[c][1]);if(this.dca)d=this.tn=="1p";else{d=b;c=String(_.Ue("client/version","1.1.0"));h=String(_.Ue("client/name","google-api-javascript-client"));h=EB[h]===!0?h:"google-api-javascript-client";
e=String(_.Ue("client/appName",""));f=[];e&&(f.push(e),f.push(" "));f.push(h);c&&(f.push("/"),f.push(c));_.nh(d,"X-JavaScript-User-Agent",f.join(""));_.nh(b,"X-Requested-With","XMLHttpRequest");d=_.mh(b,"Content-Type");a.body&&!d&&_.nh(b,"Content-Type","application/json");_.Ue("client/allowExecutableResponse")||_.nh(b,"X-Goog-Encode-Response-If-Executable","base64");(d=_.mh(b,"Content-Type"))&&d.toLowerCase()=="application/json"&&!a.params.alt&&(a.params.alt="json");(d=a.body||null)&&_.vb(d)&&(a.body=
_.Of(d));a.key=a.id;b=_.Wi(b,void 0,this.tn);d=_.ri(b);if((c=b)&&window.navigator){h=[];for(e=0;e<FB.length;e++)(f=window.navigator[FB[e]])&&h.push(encodeURIComponent(FB[e])+"="+encodeURIComponent(f));_.nh(c,"X-ClientDetails",h.join("&"))}(c=_.Ue("client/apiKey"))&&a.params.key===void 0&&(a.params.key=c);(c=_.Ue("client/trace"))&&!a.params.trace&&(a.params.trace=c)}this.tn=="auto"&&(d?this.Kj("1p"):(b=_.mh(b,"Authorization"))&&String(b).match(/^(Bearer|MAC)[ \t]/i)?this.Kj("oauth2"):this.Kj("none"));
if((b=String(a.path||"").match(/^(https?:\/\/[^\/?#]+)([\/?#].*)?$/i))&&!a.root)if(a.root=String(b[1]),a.path=String(b[2]||"/"),a.path.match(/^\/_ah\/api(\/.*)?$/))a.root+="/_ah/api",a.path=a.path.substr(8);else{b=_.Ue("googleapis.config/root");d&&(b=_.Ue("googleapis.config/root-1p")||b);b=String(b||"");c=a.root+a.path;if(h=b&&c.substr(0,b.length)===b)h=_.Ru(b),e=_.Ru(c),h=(!h.Zd&&!e.Zd||h.Og()==e.Og())&&(h.Cg==null&&e.Cg==null||h.Cg==e.Cg);h&&(a.path=c.substr(b.length),a.root=b)}b=a.params;c=_.ah(a.path);
h=String(_.Ue("googleapis.config/xd3")||"");h.length>=18&&h.substring(h.length-18)=="/static/proxy.html"&&(h=h.substring(0,h.length-18));h||(h="/");e=_.ah(h);if(h!=e)throw Error("x");h.charAt(h.length-1)!="/"&&(h+="/");c=_.yy(h,c);_.Kj(c,"/")&&(c=c.substring(0,c.length-1));h=_.ze();for(var k in b)Object.prototype.hasOwnProperty.call(b,k)&&(e=encodeURIComponent(k),h[e]=b[k]);c=_.Uh(c,h);a.path=c;a.root=gB(!!d,a.root);a.url=rB(a.path,!!d,a.root)}};
var GB=function(a){a.Fj();var b=a.kh;return{key:"gapiRequest",params:{id:b.id,key:b.key,url:b.url,path:b.path,httpMethod:b.method,body:b.body||"",headers:b.headers||{},urlParams:{},root:b.root,authType:a.tn}}};_.g=DB.prototype;_.g.execute=function(a){var b=GB(this);yB(b,function(c,d){var e=c;c.gapiRequest&&(e=c.gapiRequest);e&&e.data&&(e=e.data);c=e;c=c instanceof Array?c[0]:c;if(c.status!=204&&c.body)try{var f=_.Nf(c.body)}catch(h){}a&&a(f,d)})};
_.g.Yo=function(){var a=GB(this);(_.Ue("client/cors")||_.Ue("client/xd4"))&&tB(a)?(_.Hi(_.Gi(),15).wb(),a=wB(a)):(_.Hi(_.Gi(),14).wb(),a=uB(a));return a};_.g.hj=function(){return this.Yo()};_.g.De=function(){return this.kh.root};_.g.Dv=function(){console.log("makeJsonRpc is not supported for this request.");return{}};_.g.getFormat=function(){return 0};var FB=["appVersion","platform","userAgent"],EB={"google-api-gwt-client":!0,"google-api-javascript-client":!0};DB.prototype.execute=DB.prototype.execute;
DB.prototype.then=DB.prototype.then;DB.prototype.getPromise=DB.prototype.hj;var HB=function(a){if(!a||typeof a!="object")throw new XA("Missing rpc parameters");if(!a.method)throw new XA("Missing rpc method");this.pC=a};_.g=HB.prototype;_.g.De=function(){var a=this.pC.transport;return a?a.root||null:null};_.g.execute=function(a){var b=bB();b.add(this,{id:"gapiRpc",callback:this.Bv(a)});b.execute()};
_.g.Dv=function(a){var b=this.pC.method,c=String,d;(d=this.pC.apiVersion)||(d=String(b).split(".")[0],d=_.Ue("googleapis.config/versions/"+b)||_.Ue("googleapis.config/versions/"+d)||"v1",d=String(d));a={jsonrpc:"2.0",id:a,method:b,apiVersion:c(d)};(b=this.pC.rpcParams)&&(a.params=b);return a};
_.g.Bv=function(a){return function(b,c){if(b)if(b.error){var d=b.error;d.error==null&&(d.error=_.fk(b.error))}else d=b.result||b.data,_.vb(d)&&d.result==null&&(d.result=_.fk(b.result||b.data));else d=!1;a(d,c)}};_.g.then=function(){throw CB('The "then" method is not available on this object.');};_.g.YC=function(){};_.g.Gf=function(){};_.g.Fj=function(){};_.g.Kj=function(){};_.g.Eq=function(){};_.g.hj=function(){};HB.prototype.execute=HB.prototype.execute;var JB=function(a,b){this.Ye=b||0;this.Ye==2?(b=null,a!=null&&_.vb(a)&&(b={},b.method=a.method,b.rpcParams=a.rpcParams,b.transport=a.transport,b.root=a.root,b.apiVersion=a.apiVersion,b.authType=a.authType),this.Rb=new HB(b)):(this.Ye==0&&(b=a&&a.callback)&&(a.callback=IB(b)),b=null,a!=null&&(_.vb(a)?(b={},b.path=a.path,b.method=a.method,b.params=a.params,b.headers=a.headers,b.body=a.body,b.root=a.root,b.responseType=a.responseType,b.authType=a.authType,b.apiId=a.apiId):typeof a==="string"&&(b=a)),
this.Rb=new DB(b))},IB=function(a){return function(b){if(b!=null&&_.vb(b)&&b.error){var c=BB(b);b=_.Of([{id:"gapiRpc",error:c}]);c.error=_.su(c)}else b==null&&(b={}),c=_.su(b),c.result=_.su(b),b=_.Of([{id:"gapiRpc",result:b}]);a(c,b)}};_.g=JB.prototype;_.g.getFormat=function(){return this.Ye};_.g.execute=function(a){this.Rb.execute(a&&this.Ye==1?IB(a):a)};_.g.then=function(a,b,c){return this.Rb.then(a,b,c)};_.g.YC=function(a){this.Rb.YC(a)};_.g.Gf=function(){return this.Rb.Gf()};_.g.Fj=function(){this.Rb.Fj()};
_.g.De=function(){return this.Rb.De()};_.g.Dv=function(a){if(this.Rb.Dv)return this.Rb.Dv(a)};_.g.Kj=function(a){this.Rb.Kj(a)};_.g.Eq=function(){return!!this.Rb.Eq()};_.g.hj=function(){return this.Rb.hj()};JB.prototype.execute=JB.prototype.execute;JB.prototype.then=JB.prototype.then;JB.prototype.getPromise=JB.prototype.hj;var KB=/<response-(.*)>/,LB=/^application\/http(;.+$|$)/,MB=["clients6.google.com","content.googleapis.com","www.googleapis.com"],NB=function(a,b){a=_.mh(a,b);if(!a)throw new XA("Unable to retrieve header.");return a},OB=function(a){var b=void 0;a=_.Ca(a);for(var c=a.next();!c.done;c=a.next()){c=c.value.Gf().apiId;if(typeof c!=="string")return"batch";if(b===void 0)b=c;else if(b!=c)return"batch"}b=_.Ue("client/batchPath/"+b)||"batch/"+b.split(":").join("/");return String(b)},PB=function(a){a=a.map(function(b){return b.request});
return OB(a)},QB=function(a,b){var c=[];a=a.Gf();var d=function(f,h){_.Qm(f,function(k,l){h.push(l+": "+k)})},e={"Content-Type":"application/http","Content-Transfer-Encoding":"binary"};e["Content-ID"]="<"+b+">";d(e,c);c.push("");c.push(a.method+" "+a.path);d(a.headers,c);c.push("");a.body&&c.push(a.body);return c.join("\r\n")},TB=function(a,b){a=RB(a,b);var c={};_.Wb(a,function(d,e){c[e]=SB(d,e)});return c},SB=function(a,b){return{result:a.result||a.body,rawResult:_.Of({id:b,result:a.result||a.body}),
id:b}},RB=function(a,b){a=_.yc(a);_.Kj(a,"--")&&(a=a.substring(0,a.length-2));a=a.split(b);b=_.ze();for(var c=0;c<a.length;c++)if(a[c]){var d;if(d=a[c]){_.Kj(d,"\r\n")&&(d=d.substring(0,d.length-2));if(d){d=d.split("\r\n");for(var e=0,f={headers:{},body:""};e<d.length&&d[e]=="";)e++;for(f.outerHeaders=UB(d,e);e<d.length&&d[e]!="";)e++;e++;var h=d[e++].split(" ");f.status=Number(h[1]);f.statusText=h.slice(2).join(" ");for(f.headers=UB(d,e);e<d.length&&d[e]!="";)e++;e++;f.body=d.slice(e).join("\r\n");
qB(f);d=f}else d=null;e=_.ze();f=NB(d.outerHeaders,"Content-Type");if(LB.exec(f)==null)throw new XA("Unexpected Content-Type <"+f+">");f=NB(d.outerHeaders,"Content-ID");f=KB.exec(f);if(!f)throw new XA("Unable to recognize Content-Id.");e.id=decodeURIComponent(f[1].split("@")[0].replace(/^.*[+]/,""));e.response={status:d.status,statusText:d.statusText,headers:d.headers};d.status!=204&&(e.response.body=d.body,e.response.result=_.Nf(d.body));d=e}else d=null;d&&d.id&&(b[d.id]=d.response)}return b},UB=
function(a,b){for(var c=[];b<a.length&&a[b];b++)c.push(a[b]);return _.oh(c.join("\r\n"),!1)},VB=function(a,b,c){a=a||b;(b=!a)||(b=_.Ru(a).Ci!=="https");if(b&&(a=c?_.Ue("googleapis.config/root-1p"):_.Ue("googleapis.config/root"),!a))return!1;a=gB(c,String(a))||a;return MB.includes(_.Ru(a).Og())};var WB=function(a){YA.call(this,WB.prototype.Yo);this.qk={};this.hy={};this.Pm=[];this.Td=a;this.Eca=!!a;this.FU=this.rA=!1};_.y(WB,YA);var XB=function(a,b){a=_.Ca(Object.values(a.qk));for(var c=a.next();!c.done;c=a.next())if(c.value.map(function(d){return d.id}).includes(b))return!0;return!1};WB.prototype.Yp=function(a){(function(b){setTimeout(function(){throw b;})})(a)};
WB.prototype.add=function(a,b){var c=b||_.ze();b=_.ze();if(!a)throw new XA("Batch entry "+(_.Ae(c,"id")?'"'+c.id+'" ':"")+"is missing a request method");a.Fj();b.request=a;var d=_.Hk();d=new AB(d);b.iC=d;a.YC(b.iC.promise);d=a.Gf().headers;_.ri(d)&&(this.rA=!0);(d=String((d||{}).Authorization||"")||null)&&d.match(/^Bearer|MAC[ \t]/i)&&(this.FU=!0);d=a.Gf().root;if(!this.Eca){if(d&&this.Td&&d!=this.Td)throw new XA('The "root" provided in this request is not consistent with that of existing requests in the batch.');
this.Td=d||this.Td}if(_.Ae(c,"id")){d=c.id;if(XB(this,d))throw new XA('Batch ID "'+d+'" already in use, please use another.');b.id=d}else{do b.id=String(Math.round(2147483647*_.Pi()));while(XB(this,b.id))}b.callback=c.callback;c="batch";VB(this.Td,a.Gf().path,this.rA)&&(c=PB([b]));this.qk[c]=this.qk[c]||[];this.qk[c].push(b);this.hy[b.id]=b;return b.id};
var YB=function(a){var b=[],c=VB(a.Td,void 0,a.rA);Object.entries(a.qk).length>1&&_.Sf.warn("Heterogeneous batch requests are deprecated. See https://developers.googleblog.com/2018/03/discontinuing-support-for-json-rpc-and.html");for(var d=_.Ca(Object.entries(a.qk)),e=d.next();!e.done;e=d.next()){e=_.Ca(e.value);var f=e.next().value;e=e.next().value;for(var h=!0,k=_.Ca(e),l=k.next();!l.done;l=k.next())l=l.value,l.request.Fj(),f==="batch"&&c&&(h=!1,l.Vba=!0,l.request.Gf.root=a.Td,b.push(l.request),
a.Pm.push([l]));if(h){var m=e;f=a.Td;h=a.rA;k=a.FU;l="batch"+String(Math.round(2147483647*_.Pi()))+String(Math.round(2147483647*_.Pi()));var n="--"+l;l="multipart/mixed; boundary="+l;for(var p={path:PB(m),method:"POST"},q=[],r=0;r<m.length;r++)q.push(QB(m[r].request,[n.substr(n.indexOf("--")+2),"+",encodeURIComponent(m[r].id).split("(").join("%28").split(")").join("%29").split(".").join("%2E"),"@googleapis.com"].join("")));p.body=[n,q.join("\r\n"+n+"\r\n"),n+"--"].join("\r\n")+"\r\n";p.root=f||null;
_.Ue("client/xd4")&&fB()?(p.isXd4=!0,p.params={$ct:l},p.headers={},_.nh(p.headers,"Content-Type","text/plain; charset=UTF-8"),h?p.authType="1p":k&&(p.authType="oauth2"),f=new DB(p)):(p.headers={},_.nh(p.headers,"Content-Type",l),f=cB(p));b.push(f);a.Pm.push(e)}}return b};
WB.prototype.execute=function(a){if(!(Object.keys(this.qk).length<1)){var b=this.Bv(a);a=YB(this);var c=[],d=a.map(function(e){return new _.xk(function(f){try{e.execute(function(h,k){return f({CP:h,lea:k})})}catch(h){c.push(h),f({CP:{qz:!1,reason:h}})}})});if(c.length>0&&c.length===a.length)throw c[0];_.Fk(d).then(function(e){var f=e.map(function(h){return h.lea});e=e.map(function(h){return h.CP});b(e,f)})}};
WB.prototype.Yo=function(){var a=this;if(Object.keys(this.qk).length<1)return _.Bk({});var b=YB(this).map(function(c){return new _.xk(function(d,e){return c.hj().then(d,e)})});return $A(b).then(function(c){c=c.map(function(d){return d.qz?d.value:d});return ZB(a,c,!0)})};
WB.prototype.fY=function(a,b,c,d){var e={};if(c){e=b?RB:TB;b=NB(a.headers,"Content-Type").split("boundary=")[1];if(!b)throw new XA("Boundary not indicated in response.");e=e(a.body,"--"+b)}else b?(a.result=_.Nf(a.body),e[d]=a):e[d]=SB(a,d);a={};e=_.Ca(Object.entries(e));for(b=e.next();!b.done;b=e.next())if(c=_.Ca(b.value),b=c.next().value,c=c.next().value,a[b]=c,!this.hy[b])throw new XA("Could not find batch entry for id "+b+".");return a};
var ZB=function(a,b,c,d,e){for(var f=!1,h={},k,l=0,m=0;m<b.length;m++){var n=b[m];if(n&&Object.keys(n).includes("fulfilled")&&n.qz===!1){l++;b[m]=n.reason;n=$B([b[m]]);for(var p=_.Ca(a.Pm[m]),q=p.next();!q.done;q=p.next())h[q.value.id]=n}else{if(a.Pm[m].length<1)throw new XA("Error processing batch responses.");try{var r=!(a.Pm[m].length===1&&a.Pm[m][0].Vba),w=a.Pm[m][0].id;if(!c){p=n;q=r;var u=d[m],x=p;if(u&&(!x||!q)){var A=_.Nf(u);A&&(x=A.gapiRequest?A.gapiRequest.data:A,!q&&p&&(x.body=p))}if(!x)throw new XA("The batch response is missing.");
n=x}p=void 0;if(q=n){var C=q.headers;if(C){var F=_.ze();for(p in C)if(Object.prototype.hasOwnProperty.call(C,p)){var O=_.mh(C,p);_.nh(F,p,O,!0)}q.headers=F}}if(r&&NB(n.headers,"Content-Type").indexOf("multipart/mixed")!=0)throw new XA("The response's Content-Type is not multipart/mixed.");k=k||_.su(n);var E=eB(n);E&&!eB(k)&&(k.status=n.status,k.statusText=n.statusText);if(E||c||!r)f=!0,h=Object.assign(h,a.fY(n,c,r,w))}catch(U){for(l++,b[m]=U,n=$B([U]),p=_.Ca(a.Pm[m]),q=p.next();!q.done;q=p.next())h[q.value.id]=
n}}}if(l===b.length){d=$B(b);h=_.Of(d);k=0;a=Array.from(Object.values(a.qk)).flat();f=_.Ca(a);for(l=f.next();!l.done;l=f.next())if(l=l.value,c)l.iC.reject(d);else if(l.callback)try{k++,l.callback(d,h)}catch(U){WB.prototype.Yp(U)}if(e)try{e(d,h)}catch(U){WB.prototype.Yp(U)}else if(k!==a.length)throw b.length===1?b[0]:d;}else{if(f)for(f=_.Ca(Object.entries(h)),l=f.next();!l.done;l=f.next())if(l=_.Ca(l.value),m=l.next().value,l=l.next().value,c)m=a.hy[m],l&&eB(l)?m.iC.resolve(l):m.iC.reject(l);else if(m=
a.hy[m],m.callback){if(l&&l.rawResult)try{delete l.rawResult}catch(U){}try{m.callback(l||!1,_.Of(l))}catch(U){WB.prototype.Yp(U)}}k.result=h||{};k.body=b.length===1?k.body:"";if(e)try{e(h||null,d.length===1?d[0]:null)}catch(U){WB.prototype.Yp(U)}return k}},$B=function(a){var b={error:{code:0,message:"The batch request could not be fulfilled.  "}};a=_.Ca(a);for(var c=a.next();!c.done;c=a.next())(c=c.value)&&c.message||c instanceof Error&&c.message?b.error.message+=(c.message||c instanceof Error&&c.message)+
"  ":c&&c.error&&c.error.message&&(b.error.message+=c.error.message+"  ",b.error.code=c.error.code||b.error.code||0);b.error.message=b.error.message.trim();return{result:b,body:_.Of(b),headers:null,status:null,statusText:null}};WB.prototype.Bv=function(a){var b=this;return function(c,d){b.ME(c,d,a)}};WB.prototype.ME=function(a,b,c){ZB(this,a,!1,b,c)};WB.prototype.add=WB.prototype.add;WB.prototype.execute=WB.prototype.execute;WB.prototype.then=WB.prototype.then;var aC=function(){this.Ll=[];this.Td=this.qf=null};
aC.prototype.add=function(a,b){b=b||{};var c={},d=Object.prototype.hasOwnProperty;if(a)c.kp=a;else throw new XA("Batch entry "+(d.call(b,"id")?'"'+b.id+'" ':"")+"is missing a request method");if(d.call(b,"id")){a=b.id;for(d=0;d<this.Ll.length;d++)if(this.Ll[d].id==a)throw new XA('Batch ID "'+a+'" already in use, please use another.');c.id=a}else{do c.id=String(2147483647*_.Pi()|0);while(d.call(this.Ll,c.id))}c.callback=b.callback;this.Ll.push(c);return c.id};
var bC=function(a){return function(b){var c=b.body;if(b=b.result){for(var d={},e=b.length,f=0;f<e;++f)d[b[f].id]=b[f];a(d,c)}else a(b,c)}};
aC.prototype.execute=function(a){this.qf=[];for(var b,c,d=0;d<this.Ll.length;d++)b=this.Ll[d],c=b.kp,this.qf.push(c.Dv(b.id)),this.Td=c.De()||this.Td;c=this.Bv(a);a={requests:this.qf,root:this.Td};b={};d=a.headers||{};for(var e in d){var f=e;if(Object.prototype.hasOwnProperty.call(d,f)){var h=_.mh(d,f);h&&(f=_.kh(f,h)||_.jh(f))&&_.nh(b,f,h)}}_.nh(b,"Content-Type","application/json");e=bC(c);cB({method:"POST",root:a.root||void 0,path:"/rpc",params:a.urlParams,headers:b,body:a.requests||[]}).then(e,
e)};aC.prototype.Bv=function(a){var b=this;return function(c,d){b.ME(c,d,a)}};aC.prototype.ME=function(a,b,c){a||(a={});for(var d=0;d<this.Ll.length;d++){var e=this.Ll[d];e.callback&&e.callback(a[e.id]||!1,b)}c&&c(a,b)};dB.HP(function(){return new aC});aC.prototype.add=aC.prototype.add;aC.prototype.execute=aC.prototype.execute;var cC=function(a,b){this.Fda=a;this.Ye=b||null;this.Af=null};cC.prototype.cI=function(a){this.Ye=a;this.Af=this.Ye==2?new aC:new WB(this.Fda)};cC.prototype.add=function(a,b){if(!a)throw a=b||_.ze(),new XA("Batch entry "+(_.Ae(a,"id")?'"'+a.id+'" ':"")+"is missing a request method");this.Ye===null&&this.cI(a.getFormat());this.Ye!==a.getFormat()&&CB("Unable to add item to batch.");var c=b&&b.callback;this.Ye==1&&c&&(b.callback=function(d){d=dC(d);var e=_.Of([d]);c(d,e)});return this.Af.add(a,b)};
cC.prototype.execute=function(a){var b=a&&this.Ye==1?function(c){var d=[];_.Qm(c,function(f,h){f=dC(f);c[h]=f;d.push(f)});var e=_.Of(d);a(c,e)}:a;this.Af&&this.Af.execute(b)};var dC=function(a){var b=a?_.ru(a,"result"):null;_.vb(b)&&b.error!=null&&(b=BB(b),a={id:a.id,error:b});return a};cC.prototype.then=function(a,b,c){this.Ye==2&&CB('The "then" method is not available on this object.');return this.Af.then(a,b,c)};cC.prototype.add=cC.prototype.add;cC.prototype.execute=cC.prototype.execute;
cC.prototype.then=cC.prototype.then;var eC=function(a){YA.call(this,eC.prototype.Yo);this.Rb=a;this.XP=!1};_.y(eC,YA);var fC=function(a){a.Rb.Fj();var b=a.Rb,c=b.Gf();return!(VB(c.root,c.path,a.Rb.Eq())?OB([b])!=="batch":1)};_.g=eC.prototype;
_.g.execute=function(a){var b=this;this.XP=!0;if(fC(this))this.Rb.execute(a);else{_.Hi(_.Gi(),13).wb();var c=function(d){if(typeof a==="function"){var e={gapiRequest:{data:{status:d&&d.status,statusText:d&&d.statusText,headers:d&&d.headers,body:d&&d.body}}};if(b.getFormat()===1){a=IB(a);var f={}}var h=d?d.result:!1;d&&d.status==204&&(h=f,delete e.gapiRequest.data.body);a(h,_.Of(e))}};this.hj().then(c,c)}};
_.g.Yo=function(){if(fC(this))return this.Rb.hj();this.XP||_.Hi(_.Gi(),16).wb();return new _.xk(function(a,b){var c=aB(),d=c.add(this.Rb,{id:"gapiRequest"});c.then(function(e){var f=e.result;if(f&&(f=f[d])){Object.prototype.hasOwnProperty.call(f,"result")||(f.result=!1);Object.prototype.hasOwnProperty.call(f,"body")||(f.body="");eB(f)?a(f):b(f);return}b(e)},b)},this)};_.g.Gf=function(){if(this.Rb.Gf)return this.Rb.Gf()};_.g.Fj=function(){this.Rb.Fj&&this.Rb.Fj()};_.g.De=function(){if(this.Rb.De)return this.Rb.De()};
_.g.Kj=function(a){this.Rb.Kj&&this.Rb.Kj(a)};_.g.Eq=function(){return this.Rb.Eq()};_.g.getFormat=function(){return this.Rb.getFormat?this.Rb.getFormat():0};_.g.hj=function(){return this.Yo()};eC.prototype.execute=eC.prototype.execute;eC.prototype.then=eC.prototype.then;eC.prototype.getPromise=eC.prototype.hj;var gC="/rest?fields="+encodeURIComponent("kind,name,version,rootUrl,servicePath,resources,parameters,methods,batchPath,id")+"&pp=0",hC=function(a,b){return"/discovery/v1/apis/"+(encodeURIComponent(a)+"/"+encodeURIComponent(b)+gC)},jC=function(a,b,c,d){if(_.vb(a)){var e=a;var f=a.name;a=a.version||"v1"}else f=a,a=b;if(!f||!a)throw new XA("Missing required parameters.");var h=c||function(){},k=_.vb(d)?d:{};c=function(l){var m=l&&l.result;if(!m||m.error||!m.name||!l||l.error||l.message||l.message)h(m&&
m.error?m:l&&(l.error||l.message||l.message)?l:new XA("API discovery response missing required fields."));else{l=k.root;l=m.rootUrl!=null?String(m.rootUrl):l;l=typeof l==="string"?l.replace(/([^\/])\/$/,"$1"):void 0;k.root=l;m.name&&m.version&&!m.id&&(m.id=[m.name,m.version].join(":"));m.id&&(k.apiId=m.id,l="client/batchPath/"+m.id,m.batchPath&&!_.Ue(l)&&_.Ve(l,m.batchPath));var n=m.servicePath,p=m.parameters,q=function(w){_.Qm(w,function(u){if(!(u&&u.id&&u.path&&u.httpMethod))throw new XA("Missing required parameters");
var x=u.id.split("."),A=window.gapi.client,C;for(C=0;C<x.length-1;C++){var F=x[C];A[F]=A[F]||{};A=A[F]}var O,E;k&&(k.hasOwnProperty("root")&&(O=k.root),k.hasOwnProperty("apiId")&&(E=k.apiId));F=window.gapi.client[x[0]];F.WN||(F.WN={servicePath:n||"",parameters:p,apiId:E});x=x[C];A[x]||(A[x]=_.bb(iC,{path:typeof u.path==="string"?u.path:null,httpMethod:typeof u.httpMethod==="string"?u.httpMethod:null,parameters:u.parameters,parameterName:(u.request||{}).parameterName||"",request:u.request,root:O},
F.WN))})},r=function(w){_.Qm(w,function(u){q(u.methods);r(u.resources)})};r(m.resources);q(m.methods);h.call()}};e?c({result:e}):f.indexOf("://")>0?cB({path:f,params:{pp:0,fields:("/"+f).indexOf("/discovery/v1/apis/")>=0?"kind,name,version,rootUrl,servicePath,resources,parameters,methods,batchPath,id":'fields["kind"],fields["name"],fields["version"],fields["rootUrl"],fields["servicePath"],fields["resources"],fields["parameters"],fields["methods"],fields["batchPath"],fields["id"]'}}).then(c,c):cB({path:hC(f,
a),root:d&&d.root}).then(c,c)},iC=function(a,b,c,d,e){e=e===void 0?{}:e;var f=b.servicePath||"";_.vc(f,"/")||(f="/"+f);var h=kC(a.path,[a.parameters,b.parameters],c||{});c=h.Ed;var k=h.hha;f=_.yy(f,h.path);h=k.root;delete k.root;var l=a.parameterName;!l&&_.vy(k)==1&&k.hasOwnProperty("resource")&&(l="resource");if(l){var m=k[l];delete k[l]}m==null&&(m=d);m==null&&a.request&&(_.Eh(k)&&(k=void 0),m=k);e=e||{};l=a.httpMethod;l=="GET"&&m!==void 0&&String(m)!=""&&(_.nh(e,"X-HTTP-Method-Override",l),l="POST");
if((m==null||d!=null)&&k)for(var n in k)typeof k[n]==="string"&&(c[n]=k[n]);return cB({path:f,method:l,params:c,headers:e,body:m,root:h||a.root,apiId:b.apiId},1)},kC=function(a,b,c){c=_.fk(c);var d={};_.Pm(b,function(e){_.Qm(e,function(f,h){var k=f.required;if(f.location=="path")if(Object.prototype.hasOwnProperty.call(c,h))_.xc(a,"{"+h+"}")?(f=encodeURIComponent(String(c[h])),a=a.replace("{"+h+"}",f)):_.xc(a,"{+"+h+"}")&&(f=encodeURI(String(c[h])),a=a.replace("{+"+h+"}",f)),delete c[h];else{if(k)throw new XA("Required path parameter "+
h+" is missing.");}else f.location=="query"&&Object.prototype.hasOwnProperty.call(c,h)&&(d[h]=c[h],delete c[h])})});if(b=c.trace)d.trace=b,delete c.trace;return{path:a,Ed:d,hha:c}};var lC=function(a,b,c,d){var e=b||"v1",f=_.vb(d)?d:{root:d};if(c)jC(a,e,function(h){if(h)if(h.error)c(h);else{var k="API discovery was unsuccessful.";if(h.message||h.message)k=h.message||h.message;c({error:k,code:0})}else c()},f);else return new _.xk(function(h,k){var l=function(m){m?k(m):h()};try{jC(a,e,l,f)}catch(m){k(m)}})},mC=new RegExp(/^((([Hh][Tt][Tt][Pp][Ss]?:)?\/\/[^\/?#]*)?\/)?/.source+/(_ah\/api\/)?(batch|rpc)(\/|\?|#|$)/.source),nC=function(a,b){if(!a)throw new XA("Missing required parameters");
var c=typeof a==="object"?a:{path:a};a=c.callback;delete c.callback;b=new JB(c,b);if(c=!!_.Ue("client/xd4")&&fB()){var d=b.Gf();c=d.path;(d=d.root)&&d.charAt(d.length-1)!=="/"&&(d+="/");d&&c&&c.substr(0,d.length)===d&&(c=c.substr(d.length));c=!c.match(mC)}c&&(b=new eC(b));return a?(b.execute(a),null):b};dB.IP(function(a){return nC.apply(null,arguments)});
var oC=function(a,b){if(!a)throw new XA("Missing required parameters");for(var c=a.split("."),d=window.gapi.client,e=0;e<c.length-1;e++){var f=c[e];d[f]=d[f]||{};d=d[f]}c=c[c.length-1];if(!d[c]){var h=b||{};d[c]=function(k){var l=typeof h=="string"?h:h.root;k&&k.root&&(l=k.root);return new JB({method:a,apiVersion:h.apiVersion,rpcParams:k,transport:{name:"googleapis",root:l}},2)}}},pC=function(a){return new cC(a)};dB.GP(function(a){return pC.apply(null,arguments)});
var qC=function(a){if(_.zB.JSONRPC_ERROR_MOD)throw new XA(a+" is discontinued. See https://developers.googleblog.com/2018/03/discontinuing-support-for-json-rpc-and.html");_.Sf.log(a+" is deprecated. See https://developers.google.com/api-client-library/javascript/reference/referencedocs")};_.t("gapi.client.init",function(a){a.apiKey&&_.Ve("client/apiKey",a.apiKey);var b=_.Ib(a.discoveryDocs||[],function(d){return lC(d)});if((a.clientId||a.client_id)&&a.scope){var c=new _.xk(function(d,e){var f=function(){_.Xa.gapi.auth2.init.call(_.Xa.gapi.auth2,a).then(function(){d()},e)};ZA?f():_.Xa.gapi.load("auth2",{callback:function(){f()},onerror:function(h){e(h||Error("Ca"))}})});b.push(c)}else(a.clientId||a.client_id||a.scope)&&_.Sf.log("client_id and scope must both be provided to initialize OAuth.");
return _.Fk(b).then(function(){})});_.t("gapi.client.load",lC);_.t("gapi.client.newBatch",pC);_.t("gapi.client.newRpcBatch",function(){qC("gapi.client.newRpcBatch");return pC()});_.t("gapi.client.newHttpBatch",function(a){qC("gapi.client.newHttpBatch");return new cC(a,0)});_.t("gapi.client.register",function(a,b){qC("gapi.client.register");var c;b&&(c={apiVersion:b.apiVersion,root:b.root});oC(a,c)});_.t("gapi.client.request",nC);
_.t("gapi.client.rpcRequest",function(a,b,c){qC("gapi.client.rpcRequest");if(!a)throw new XA('Missing required parameter "method".');return new JB({method:a,apiVersion:b,rpcParams:c,transport:{name:"googleapis",root:c&&c.root||""}},2)});_.t("gapi.client.setApiKey",function(a){_.Ve("client/apiKey",a);_.Ve("googleapis.config/developerKey",a)});_.t("gapi.client.setApiVersions",function(a){qC("gapi.client.setApiVersions");_.Ve("googleapis.config/versions",a)});_.t("gapi.client.getToken",function(a){return _.ui(a)});
_.t("gapi.client.setToken",function(a,b){a?_.Hw(a,b):_.Iw(b)});_.t("gapi.client.AuthType",{Sha:"auto",NONE:"none",Ila:"oauth2",Pja:"1p"});_.t("gapi.client.AuthType.AUTO","auto");_.t("gapi.client.AuthType.NONE","none");_.t("gapi.client.AuthType.OAUTH2","oauth2");_.t("gapi.client.AuthType.FIRST_PARTY","1p");
});
// Google Inc.
